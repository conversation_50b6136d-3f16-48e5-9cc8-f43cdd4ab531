﻿<!--
    /// Author : <PERSON><PERSON> <PERSON>
    /// Created On :13-07-2022
    /// YouTube Channel : C# Design Pro 
 -->
<Window x:Class="Dock_Bar_App.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Dock_Bar_App"
        mc:Ignorable="d"
        Title="MainWindow"
        Height="740"
        Width="1220"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        ResizeMode="NoResize"
        Background="Transparent"
        AllowsTransparency="True">

    <Grid>

        <!--// Background //-->

        <Border Background="#1C1D31"
                CornerRadius="25" />

        <!--// Main Grid //-->

        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="280" />
                <ColumnDefinition Width="280" />
                <ColumnDefinition Width="280" />
                <ColumnDefinition Width="280" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Grid.RowDefinitions>
                <RowDefinition Height="140" />
                <RowDefinition Height="360" />
                <RowDefinition Height="170" />
                <RowDefinition Height="70" />
            </Grid.RowDefinitions>

            <!--// Panel 1 //-->

            <Border Grid.Row="1"
                    Grid.Column="1"
                    CornerRadius="20"
                    Margin="20">

                <Border.Background>

                    <LinearGradientBrush StartPoint="0.75,0"
                                         EndPoint="0,1">

                        <GradientStop Color="#DF208F"
                                      Offset="0.1" />

                        <GradientStop Color="#DFCF20"
                                      Offset="0.8" />

                    </LinearGradientBrush>

                </Border.Background>

                <Grid>

                    <Border Background="#FFFFFF"
                            Height="200"
                            Width="180"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                            CornerRadius="20"
                            Opacity="0.3"
                            Margin="10,10,0,0" />

                    <TextBlock Text="WORKING &#x0a;CAPITAL &#x0a;$392.4K"
                               Foreground="#FFFFFF"
                               FontFamily="Oswald"
                               FontSize="32"
                               VerticalAlignment="Top"
                               HorizontalAlignment="Left"
                               Margin="30,20,0,0" />

                </Grid>

            </Border>

            <!--// Panel 2 //-->

            <Border Grid.Row="1"
                    Grid.Column="2"
                    CornerRadius="20"
                    Margin="20">

                <Border.Background>

                    <LinearGradientBrush StartPoint="0.75,0"
                                         EndPoint="0,1">

                        <GradientStop Color="#27BDE9"
                                      Offset="0.1" />

                        <GradientStop Color="#ABDB54"
                                      Offset="0.8" />

                    </LinearGradientBrush>

                </Border.Background>

                <Grid>

                    <Border Background="#FFFFFF"
                            Height="200"
                            Width="180"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                            CornerRadius="20"
                            Opacity="0.3"
                            Margin="10,10,0,0" />

                    <TextBlock Text="DAILY &#x0a;EXPENSES &#x0a;$15.6K"
                               Foreground="#FFFFFF"
                               FontFamily="Oswald"
                               FontSize="32"
                               VerticalAlignment="Top"
                               HorizontalAlignment="Left"
                               Margin="30,20,0,0" />

                </Grid>

            </Border>

            <!--// Panel 3 //-->

            <Border Grid.Row="1"
                    Grid.Column="3"
                    CornerRadius="20"
                    Margin="20">

                <Border.Background>

                    <LinearGradientBrush StartPoint="0.75,0"
                                         EndPoint="0,1">

                        <GradientStop Color="#F96100"
                                      Offset="0.1" />

                        <GradientStop Color="#E70B75"
                                      Offset="0.8" />

                    </LinearGradientBrush>

                </Border.Background>

                <Grid>

                    <Border Background="#FFFFFF"
                            Height="200"
                            Width="180"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                            CornerRadius="20"
                            Opacity="0.3"
                            Margin="10,10,0,0" />

                    <TextBlock Text="ACCOUNTS &#x0a;PAYABLE &#x0a;$10.7K"
                               Foreground="#FFFFFF"
                               FontFamily="Oswald"
                               FontSize="32"
                               VerticalAlignment="Top"
                               HorizontalAlignment="Left"
                               Margin="30,20,0,0" />

                </Grid>

            </Border>

            <!--// Panel 4 //-->

            <Border Grid.Row="1"
                    Grid.Column="4"
                    CornerRadius="20"
                    Margin="20">

                <Border.Background>

                    <LinearGradientBrush StartPoint="0.75,0"
                                         EndPoint="0,1">

                        <GradientStop Color="#06A4FB"
                                      Offset="0.1" />

                        <GradientStop Color="#B84EEB"
                                      Offset="0.8" />

                    </LinearGradientBrush>

                </Border.Background>

                <Grid>

                    <Border Background="#FFFFFF"
                            Height="200"
                            Width="180"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                            CornerRadius="20"
                            Opacity="0.3"
                            Margin="10,10,0,0" />

                    <TextBlock Text="REVENUE &#x0a;GROWTH &#x0a;$62.8K"
                               Foreground="#FFFFFF"
                               FontFamily="Oswald"
                               FontSize="32"
                               VerticalAlignment="Top"
                               HorizontalAlignment="Left"
                               Margin="30,20,0,0" />

                </Grid>

            </Border>

            <!--// Header //-->

            <TextBlock Grid.Row="0"
                       Grid.Column="1"
                       Grid.ColumnSpan="2"
                       Text="C #   D E S I G N   P R O"
                       Foreground="#CDD7E0"
                       FontFamily="Oswald"
                       FontSize="36"
                       Margin="20,50,0,0" />

            <TextBlock Grid.Row="0"
                       Grid.Column="1"
                       Grid.ColumnSpan="2"
                       Text="D E S I G N   T H E   I N T E R F A C E"
                       Foreground="#A6B8C8"
                       FontFamily="Roboto"
                       FontSize="12"
                       HorizontalAlignment="Right"
                       VerticalAlignment="Bottom"
                       Margin="0,0,238,23" />

            <!--// Footer //-->

            <Border Grid.Row="3"
                    Grid.ColumnSpan="6"
                    CornerRadius="0,0,20,20">

                <Border.Background>

                    <LinearGradientBrush StartPoint="0.75,0"
                                         EndPoint="0,1">

                        <GradientStop Color="#DF208F"
                                      Offset="0.1" />

                        <GradientStop Color="#DFCF20"
                                      Offset="0.8" />

                    </LinearGradientBrush>

                </Border.Background>

            </Border>

            <!--// Dock Bar //-->

            <Grid Grid.Row="2"
                  Grid.Column="1"
                  Grid.ColumnSpan="4">

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="740" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <Border Grid.Column="1"
                        Background="#323345"
                        CornerRadius="10,10,0,0"
                        Margin="0,85,0,0">

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20" />
                            <ColumnDefinition Width="70" />
                            <ColumnDefinition Width="70" />
                            <ColumnDefinition Width="70" />
                            <ColumnDefinition Width="70" />
                            <ColumnDefinition Width="70" />
                            <ColumnDefinition Width="70" />
                            <ColumnDefinition Width="70" />
                            <ColumnDefinition Width="70" />
                            <ColumnDefinition Width="70" />
                            <ColumnDefinition Width="70" />
                            <ColumnDefinition Width="20" />
                        </Grid.ColumnDefinitions>

                        <!--// Home //-->

                        <Button Grid.Column="1"
                                x:Name="home"
                                Style="{StaticResource DockBtnStyle}">

                            <Button.Background>

                                <ImageBrush ImageSource="Assets/img_home.png"
                                            Stretch="Uniform" />

                            </Button.Background>

                            <Popup Width="70"
                                   HorizontalOffset="-12"
                                   PlacementTarget="{Binding ElementName=home}"
                                   IsOpen="{Binding ElementName=home, Path=IsMouseOver, Mode=OneWay}"
                                   Style="{StaticResource PopupStyle}">

                                <Grid>

                                    <Border Style="{StaticResource border}">

                                        <TextBlock Text="Home"
                                                   Style="{StaticResource PopupText}" />

                                    </Border>

                                    <Path Style="{StaticResource ArrowPath}" />

                                </Grid>

                            </Popup>

                        </Button>

                        <!--// Settings //-->

                        <Button Grid.Column="2"
                                x:Name="setting"
                                Style="{StaticResource DockBtnStyle}">

                            <Button.Background>

                                <ImageBrush ImageSource="Assets/img_settings.png"
                                            Stretch="Uniform" />

                            </Button.Background>

                            <Popup Width="85"
                                   HorizontalOffset="-20"
                                   PlacementTarget="{Binding ElementName=setting}"
                                   IsOpen="{Binding ElementName=setting, Path=IsMouseOver, Mode=OneWay}"
                                   Style="{StaticResource PopupStyle}">

                                <Grid>

                                    <Border Style="{StaticResource border}">

                                        <TextBlock Text="Settings"
                                                   Style="{StaticResource PopupText}" />

                                    </Border>

                                    <Path Style="{StaticResource ArrowPath}" />

                                </Grid>

                            </Popup>

                        </Button>

                        <!--// Mail //-->

                        <Button Grid.Column="3"
                                x:Name="mail"
                                Style="{StaticResource DockBtnStyle}">

                            <Button.Background>

                                <ImageBrush ImageSource="Assets/img_mail.png"
                                            Stretch="Uniform" />

                            </Button.Background>

                            <Popup Width="60"
                                   HorizontalOffset="-6"
                                   PlacementTarget="{Binding ElementName=mail}"
                                   IsOpen="{Binding ElementName=mail, Path=IsMouseOver, Mode=OneWay}"
                                   Style="{StaticResource PopupStyle}">

                                <Grid>

                                    <Border Style="{StaticResource border}">

                                        <TextBlock Text="Mail"
                                                   Style="{StaticResource PopupText}" />

                                    </Border>

                                    <Path Style="{StaticResource ArrowPath}" />

                                </Grid>

                            </Popup>

                        </Button>

                        <!--// Visual Studio //-->

                        <Button Grid.Column="4"
                                x:Name="vs"
                                Style="{StaticResource DockBtnStyle}">

                            <Button.Background>

                                <ImageBrush ImageSource="Assets/img_vs.png"
                                            Stretch="Uniform" />

                            </Button.Background>

                            <Popup Width="118"
                                   HorizontalOffset="-35"
                                   PlacementTarget="{Binding ElementName=vs}"
                                   IsOpen="{Binding ElementName=vs, Path=IsMouseOver, Mode=OneWay}"
                                   Style="{StaticResource PopupStyle}">

                                <Grid>

                                    <Border Style="{StaticResource border}">

                                        <TextBlock Text="Visual Studio"
                                                   Style="{StaticResource PopupText}" />

                                    </Border>

                                    <Path Style="{StaticResource ArrowPath}" />

                                </Grid>

                            </Popup>

                        </Button>

                        <!--// Chrome //-->

                        <Button Grid.Column="5"
                                x:Name="chrome"
                                Style="{StaticResource DockBtnStyle}">

                            <Button.Background>

                                <ImageBrush ImageSource="Assets/img_chrome.png"
                                            Stretch="Uniform" />

                            </Button.Background>

                            <Popup Width="85"
                                   HorizontalOffset="-19"
                                   PlacementTarget="{Binding ElementName=chrome}"
                                   IsOpen="{Binding ElementName=chrome, Path=IsMouseOver, Mode=OneWay}"
                                   Style="{StaticResource PopupStyle}">

                                <Grid>

                                    <Border Style="{StaticResource border}">

                                        <TextBlock Text="Chrome"
                                                   Style="{StaticResource PopupText}" />

                                    </Border>

                                    <Path Style="{StaticResource ArrowPath}" />

                                </Grid>

                            </Popup>

                        </Button>

                        <!--// YouTube //-->

                        <Button Grid.Column="6"
                                x:Name="youtube"
                                Style="{StaticResource DockBtnStyle}">

                            <Button.Background>

                                <ImageBrush ImageSource="Assets/img_youtube.png"
                                            Stretch="Uniform" />

                            </Button.Background>

                            <Popup Width="85"
                                   HorizontalOffset="-18"
                                   PlacementTarget="{Binding ElementName=youtube}"
                                   IsOpen="{Binding ElementName=youtube, Path=IsMouseOver, Mode=OneWay}"
                                   Style="{StaticResource PopupStyle}">

                                <Grid>

                                    <Border Style="{StaticResource border}">

                                        <TextBlock Text="YouTube"
                                                   Style="{StaticResource PopupText}" />

                                    </Border>

                                    <Path Style="{StaticResource ArrowPath}" />

                                </Grid>

                            </Popup>

                        </Button>

                        <!--// Word //-->

                        <Button Grid.Column="7"
                                x:Name="word"
                                Style="{StaticResource DockBtnStyle}">

                            <Button.Background>

                                <ImageBrush ImageSource="Assets/img_word.png"
                                            Stretch="Uniform" />

                            </Button.Background>

                            <Popup Width="60"
                                   HorizontalOffset="-8"
                                   PlacementTarget="{Binding ElementName=word}"
                                   IsOpen="{Binding ElementName=word, Path=IsMouseOver, Mode=OneWay}"
                                   Style="{StaticResource PopupStyle}">

                                <Grid>

                                    <Border Style="{StaticResource border}">

                                        <TextBlock Text="Word"
                                                   Style="{StaticResource PopupText}" />

                                    </Border>

                                    <Path Style="{StaticResource ArrowPath}" />

                                </Grid>

                            </Popup>

                        </Button>

                        <!--// Photoshop //-->

                        <Button Grid.Column="8"
                                x:Name="ps"
                                Style="{StaticResource DockBtnStyle}">

                            <Button.Background>

                                <ImageBrush ImageSource="Assets/img_photoshop.png"
                                            Stretch="Uniform" />

                            </Button.Background>

                            <Popup Width="105"
                                   HorizontalOffset="-27"
                                   PlacementTarget="{Binding ElementName=ps}"
                                   IsOpen="{Binding ElementName=ps, Path=IsMouseOver, Mode=OneWay}"
                                   Style="{StaticResource PopupStyle}">

                                <Grid>

                                    <Border Style="{StaticResource border}">

                                        <TextBlock Text="Photoshop"
                                                   Style="{StaticResource PopupText}" />

                                    </Border>

                                    <Path Style="{StaticResource ArrowPath}" />

                                </Grid>

                            </Popup>

                        </Button>

                        <!--// Downloads //-->

                        <Button Grid.Column="9"
                                x:Name="download"
                                Style="{StaticResource DockBtnStyle}">

                            <Button.Background>

                                <ImageBrush ImageSource="Assets/img_downloads.png"
                                            Stretch="Uniform" />

                            </Button.Background>

                            <Popup Width="105"
                                   HorizontalOffset="-28"
                                   PlacementTarget="{Binding ElementName=download}"
                                   IsOpen="{Binding ElementName=download, Path=IsMouseOver, Mode=OneWay}"
                                   Style="{StaticResource PopupStyle}">

                                <Grid>

                                    <Border Style="{StaticResource border}">

                                        <TextBlock Text="Downloads"
                                                   Style="{StaticResource PopupText}" />

                                    </Border>

                                    <Path Style="{StaticResource ArrowPath}" />

                                </Grid>

                            </Popup>

                        </Button>

                        <!--// Trash //-->

                        <Button Grid.Column="10"
                                x:Name="trash"
                                Style="{StaticResource DockBtnStyle}">

                            <Button.Background>

                                <ImageBrush ImageSource="Assets/img_trash.png"
                                            Stretch="Uniform" />

                            </Button.Background>

                            <Popup Width="65"
                                   HorizontalOffset="-10"
                                   PlacementTarget="{Binding ElementName=trash}"
                                   IsOpen="{Binding ElementName=trash, Path=IsMouseOver, Mode=OneWay}"
                                   Style="{StaticResource PopupStyle}">

                                <Grid>

                                    <Border Style="{StaticResource border}">

                                        <TextBlock Text="Trash"
                                                   Style="{StaticResource PopupText}" />

                                    </Border>

                                    <Path Style="{StaticResource ArrowPath}" />

                                </Grid>

                            </Popup>

                        </Button>

                    </Grid>

                </Border>

            </Grid>

        </Grid>

        <!--// User //-->

        <Grid Height="50"
              Width="50"
              HorizontalAlignment="Right"
              VerticalAlignment="Top"
              Margin="0,30,73,0">

            <Button x:Name="user"
                    Style="{StaticResource UserStyle}">

                <Button.Background>

                    <ImageBrush ImageSource="Assets/img_user.png"
                                Stretch="Uniform" />

                </Button.Background>

                <Popup Width="137"
                       HorizontalOffset="-93"
                       PlacementTarget="{Binding ElementName=user}"
                       IsOpen="{Binding ElementName=user, Path=IsMouseOver, Mode=OneWay}"
                       Style="{StaticResource user_popup}">

                    <Grid>

                        <Border Style="{StaticResource user_border}">

                            <TextBlock Text="Arun Mutharasu"
                                       Style="{StaticResource PopupText}" />

                        </Border>

                        <Path Style="{StaticResource user_arrow_path}" />

                    </Grid>

                </Popup>

            </Button>

        </Grid>

        <!--// Exit Button //-->

        <Button x:Name="ExitBtn"
                Style="{StaticResource CloseBtnStyle}"
                Click="ExitBtn_Click" />

    </Grid>

</Window>
