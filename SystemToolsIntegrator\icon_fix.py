#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图标修复工具
解决Python默认图标问题
"""

import tkinter as tk
from pathlib import Path
import sys
import os

def fix_window_icon(window, icon_path=None):
    """修复窗口图标"""
    if icon_path is None:
        icon_path = Path(__file__).parent / "1.ico"

    # 确保icon_path是Path对象
    if isinstance(icon_path, str):
        icon_path = Path(icon_path)

    if not icon_path.exists():
        print(f"图标文件不存在: {icon_path}")
        return False
    
    try:
        # 方法1: 使用iconbitmap
        window.iconbitmap(str(icon_path))
        
        # 方法2: 使用wm_iconbitmap (更强制)
        window.wm_iconbitmap(str(icon_path))
        
        # 方法3: 设置窗口属性
        if sys.platform == "win32":
            try:
                import ctypes
                from ctypes import wintypes
                
                # 获取窗口句柄
                hwnd = window.winfo_id()
                
                # 加载图标
                hicon = ctypes.windll.user32.LoadImageW(
                    None,
                    str(icon_path),
                    1,  # IMAGE_ICON
                    0, 0,
                    0x00000010  # LR_LOADFROMFILE
                )
                
                if hicon:
                    # 设置大图标
                    ctypes.windll.user32.SendMessageW(
                        hwnd,
                        0x0080,  # WM_SETICON
                        1,       # ICON_BIG
                        hicon
                    )
                    
                    # 设置小图标
                    ctypes.windll.user32.SendMessageW(
                        hwnd,
                        0x0080,  # WM_SETICON
                        0,       # ICON_SMALL
                        hicon
                    )
                    
                    print("✓ 使用Windows API设置图标成功")
                    
            except Exception as e:
                print(f"Windows API设置图标失败: {e}")
        
        print("✓ 图标设置成功")
        return True
        
    except Exception as e:
        print(f"设置图标失败: {e}")
        return False

def create_icon_if_missing():
    """如果图标不存在，创建一个简单的图标"""
    icon_path = Path(__file__).parent / "1.ico"
    
    if icon_path.exists():
        return str(icon_path)
    
    try:
        # 创建一个简单的ICO文件
        # 这里使用PIL创建一个简单图标
        try:
            from PIL import Image, ImageDraw
            
            # 创建32x32的图像
            img = Image.new('RGBA', (32, 32), (102, 126, 234, 255))
            draw = ImageDraw.Draw(img)
            
            # 绘制一个简单的星形
            points = [
                (16, 2), (20, 12), (30, 12), (22, 20),
                (26, 30), (16, 24), (6, 30), (10, 20),
                (2, 12), (12, 12)
            ]
            draw.polygon(points, fill=(255, 255, 255, 255))
            
            # 保存为ICO格式
            img.save(str(icon_path), format='ICO', sizes=[(32, 32)])
            print(f"✓ 创建图标文件: {icon_path}")
            
        except ImportError:
            # 如果没有PIL，创建一个空的ICO文件头
            ico_header = bytes([
                0x00, 0x00,  # Reserved
                0x01, 0x00,  # Type (1 = ICO)
                0x01, 0x00,  # Number of images
                0x20,        # Width (32)
                0x20,        # Height (32)
                0x00,        # Color count
                0x00,        # Reserved
                0x01, 0x00,  # Planes
                0x20, 0x00,  # Bits per pixel
                0x00, 0x00, 0x00, 0x00,  # Size of image data
                0x16, 0x00, 0x00, 0x00   # Offset to image data
            ])
            
            with open(icon_path, 'wb') as f:
                f.write(ico_header)
                # 写入一些基本的图像数据
                f.write(b'\x00' * 1024)
            
            print(f"✓ 创建基础图标文件: {icon_path}")
            
        return str(icon_path)
        
    except Exception as e:
        print(f"创建图标文件失败: {e}")
        return None

# test_icon_fix函数已完全移除，避免在PyInstaller打包时被执行

if __name__ == "__main__":
    # 测试功能已移除，避免在PyInstaller打包时执行
    pass
