#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版EXE保护工具 - 重度加密但避免复杂错误
"""

import os
import sys
import shutil
import hashlib
import base64
import time
import random
import tempfile
import subprocess
from pathlib import Path

def calculate_file_hash(file_path):
    """计算文件哈希值"""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

def heavy_encrypt_exe(exe_path):
    """重度加密EXE文件"""
    
    print(f"🔒 开始重度加密: {exe_path}")
    
    # 读取原始EXE
    with open(exe_path, 'rb') as f:
        original_data = f.read()
    
    print(f"✓ 读取EXE: {len(original_data)} 字节")
    
    # 计算哈希
    file_hash = calculate_file_hash(exe_path)
    print(f"✓ 文件哈希: {file_hash[:16]}...")
    
    # 重度加密过程
    print("🔐 执行4层重度加密...")
    
    # 第1层：XOR加密
    xor_key = b'StarCafeAntiCheat2025SecretKey!@#$%^&*()_+ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    xor_data = bytearray()
    for i, byte in enumerate(original_data):
        xor_data.append(byte ^ xor_key[i % len(xor_key)])
    
    # 第2层：Base64编码
    b64_data = base64.b64encode(bytes(xor_data))
    
    # 第3层：数据膨胀 - 添加大量随机填充
    random.seed(12345)  # 固定种子
    padded_data = bytearray()
    for i, byte in enumerate(b64_data):
        padded_data.append(byte)
        # 每个字节后添加2-4个随机填充字节
        padding_count = random.randint(2, 4)
        for _ in range(padding_count):
            padded_data.append(random.randint(0, 255))
    
    # 第4层：最终Base64编码
    final_encrypted = base64.b64encode(bytes(padded_data)).decode('utf-8')
    
    print(f"✓ 重度加密完成: {len(original_data)} → {len(final_encrypted)} 字符")
    print(f"✓ 膨胀率: {len(final_encrypted)/len(original_data):.1f}x")
    
    return final_encrypted, file_hash

def create_protection_shell(encrypted_data, original_hash):
    """创建保护壳代码"""
    
    # 将加密数据分块（每块30KB）
    chunk_size = 30000
    chunks = []
    for i in range(0, len(encrypted_data), chunk_size):
        chunk = encrypted_data[i:i+chunk_size]
        chunks.append(chunk)
    
    print(f"✓ 数据分块: {len(chunks)} 个块")
    
    # 生成分块变量定义
    chunks_code = ""
    for i, chunk in enumerate(chunks):
        chunks_code += f'CHUNK_{i} = """{chunk}"""\n'
    
    # 创建保护壳
    shell_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重度保护的EXE启动器
"""

import os
import sys
import base64
import tempfile
import subprocess
import hashlib
import time
import random
import uuid

# 分块存储的加密数据
{chunks_code}

# 原始文件哈希
ORIGINAL_HASH = "{original_hash}"

# 解密密钥
XOR_KEY = b'StarCafeAntiCheat2025SecretKey!@#$%^&*()_+ABCDEFGHIJKLMNOPQRSTUVWXYZ'

def anti_debug_check():
    """反调试检查"""
    try:
        # 检查调试环境变量
        debug_vars = ['PYDEVD_LOAD_VALUES_ASYNC', 'PYCHARM_HOSTED', 'VSCODE_PID']
        for var in debug_vars:
            if var in os.environ:
                time.sleep(random.randint(2, 5))
                return False
        
        # 检查调试工具进程
        try:
            import psutil
            debug_processes = ['ollydbg.exe', 'x64dbg.exe', 'ida.exe', 'cheatengine.exe']
            for proc in psutil.process_iter(['name']):
                try:
                    if proc.info['name'].lower() in debug_processes:
                        time.sleep(random.randint(3, 6))
                        return False
                except:
                    pass
        except ImportError:
            pass
        
        return True
    except:
        return True

def decrypt_and_run():
    """解密并运行"""
    try:
        print("🔓 开始重度解密...")
        
        # 反调试检查
        if not anti_debug_check():
            print("❌ 检测到调试环境")
            return False
        
        # 重组加密数据
        encrypted_data = ""
        chunk_num = 0
        while True:
            chunk_var = f'CHUNK_{{chunk_num}}'
            if chunk_var in globals():
                encrypted_data += globals()[chunk_var]
                chunk_num += 1
            else:
                break
        
        print(f"✓ 重组数据: {{len(encrypted_data)}} 字符")
        
        # 第1层解密：Base64解码
        padded_data = base64.b64decode(encrypted_data.encode('utf-8'))
        
        # 第2层解密：移除填充
        random.seed(12345)
        b64_data = bytearray()
        i = 0
        while i < len(padded_data):
            b64_data.append(padded_data[i])
            padding_count = random.randint(2, 4)
            i += 1 + padding_count
        
        # 第3层解密：Base64解码
        xor_data = base64.b64decode(bytes(b64_data))
        
        # 第4层解密：XOR解密
        original_data = bytearray()
        for i, byte in enumerate(xor_data):
            original_data.append(byte ^ XOR_KEY[i % len(XOR_KEY)])
        
        print(f"✓ 解密完成: {{len(original_data)}} 字节")
        
        # 验证哈希
        temp_hash = hashlib.sha256(bytes(original_data)).hexdigest()
        if temp_hash != ORIGINAL_HASH:
            print("❌ 完整性验证失败")
            return False
        
        print("✓ 完整性验证通过")
        
        # 创建临时文件并运行
        temp_name = f"tmp_{{uuid.uuid4().hex[:8]}}.exe"
        temp_path = os.path.join(tempfile.gettempdir(), temp_name)
        
        with open(temp_path, 'wb') as f:
            f.write(bytes(original_data))
        
        print("🚀 启动程序...")
        
        try:
            result = subprocess.run([temp_path], check=False)
            print(f"✓ 程序执行完成: {{result.returncode}}")
            return True
        finally:
            # 清理临时文件
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                print("✓ 临时文件已清理")
            except:
                pass
        
    except Exception as e:
        print(f"❌ 运行失败: {{e}}")
        return False

if __name__ == "__main__":
    try:
        success = decrypt_and_run()
        if not success:
            print("程序运行失败")
            time.sleep(3)  # 替换input为延迟
    except KeyboardInterrupt:
        print("\\n用户中断程序")
    except Exception as e:
        print(f"程序异常: {{e}}")
        time.sleep(3)  # 替换input为延迟
'''
    
    return shell_code

def build_protected_exe(shell_code, original_exe_path):
    """构建受保护的EXE"""
    
    print("🔨 构建受保护的EXE...")
    
    # 创建临时Python文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as temp_py:
        temp_py.write(shell_code)
        temp_py_path = temp_py.name
    
    # 获取文件名
    original_name = Path(original_exe_path).stem
    protected_name = f"{original_name}_重度保护版"
    
    # 创建spec文件
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
import sys
import os

block_cipher = None

a = Analysis(
    [r'{temp_py_path}'],
    pathex=[os.path.abspath('.')],
    binaries=[],
    datas=[],
    hiddenimports=['psutil', 'tempfile', 'subprocess', 'hashlib', 'base64', 'time', 'random', 'uuid'],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
    name='{protected_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    runtime_tmpdir=None,
    console=False,  # 无控制台模式
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    uac_admin=False,
    icon='1.ico' if os.path.exists('1.ico') else None
)
'''
    
    spec_path = 'protected.spec'
    with open(spec_path, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    try:
        # 清理旧文件
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # 构建
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            spec_path
        ], check=True, capture_output=True, text=True)
        
        print("✓ 构建成功")
        
        # 检查结果
        protected_exe_path = Path(f'dist/{protected_name}.exe')
        if protected_exe_path.exists():
            file_size = protected_exe_path.stat().st_size / (1024 * 1024)
            print(f"✅ 受保护的EXE构建完成!")
            print(f"📁 文件路径: {protected_exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            
            # 清理临时文件
            os.remove(spec_path)
            os.remove(temp_py_path)
            if os.path.exists('build'):
                shutil.rmtree('build')
            
            return str(protected_exe_path)
        else:
            print("❌ 未找到生成的EXE")
            return None
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 70)
    print("简化版EXE保护工具 - 重度加密版")
    print("=" * 70)
    
    # 查找EXE文件
    exe_files = []
    if os.path.exists('dist'):
        for file in os.listdir('dist'):
            if file.endswith('.exe') and '保护版' not in file:
                exe_files.append(os.path.join('dist', file))
    
    if not exe_files:
        print("❌ 未找到可保护的EXE文件")
        return
    
    # 使用最新的EXE
    original_exe = max(exe_files, key=os.path.getmtime)
    print(f"🎯 目标EXE: {original_exe}")
    
    print("\\n🔒 重度保护特性:")
    print("- ✅ 4层重度加密")
    print("- ✅ 数据膨胀保护")
    print("- ✅ 反调试检查")
    print("- ✅ 进程监控")
    print("- ✅ 完整性验证")
    print("- ✅ 安全临时文件")
    
    # 加密EXE
    encrypted_data, file_hash = heavy_encrypt_exe(original_exe)
    
    # 创建保护壳
    shell_code = create_protection_shell(encrypted_data, file_hash)
    
    # 构建受保护的EXE
    protected_exe = build_protected_exe(shell_code, original_exe)
    
    if protected_exe:
        print("\\n🎊 重度保护完成!")
        print(f"📁 受保护的EXE: {protected_exe}")
        print("\\n✨ 保护效果:")
        print("- 原始EXE经过4层重度加密")
        print("- 数据膨胀5-8倍，大幅增加逆向难度")
        print("- 运行时多重安全检查")
        print("- 可在多台机器使用")
    else:
        print("\\n❌ 保护失败!")

if __name__ == "__main__":
    main()
