﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!--// Dock Bar Button Style //-->

    <Style x:Key="DockBtnStyle"
           TargetType="Button">

        <Setter Property="Height"
                Value="50" />

        <Setter Property="Width"
                Value="50" />

        <Setter Property="Template">

            <Setter.Value>

                <ControlTemplate TargetType="{x:Type Button}">

                    <Border x:Name="border"
                            BorderThickness="0"
                            Background="{TemplateBinding Background}" />

                </ControlTemplate>
                
            </Setter.Value>
            
        </Setter>
        
        <!--// Button Animation //-->

        <Style.Triggers>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="(Button.Width)"
                                         To="65"
                                         Duration="0:0:0.3" />

                        <DoubleAnimation Storyboard.TargetProperty="(Button.Height)"
                                         To="65"
                                         Duration="0:0:0.3" />
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>

            <EventTrigger RoutedEvent="MouseLeave">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="(Button.Width)"
                                         To="50"
                                         Duration="0:0:0.3" />

                        <DoubleAnimation Storyboard.TargetProperty="(Button.Height)"
                                         To="50"
                                         Duration="0:0:0.3" />
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>
    
    <!--// Dock Bar Button Popup //-->

    <Style x:Key="PopupStyle"
           TargetType="Popup">

        <Setter Property="AllowsTransparency"
                Value="True" />

        <Setter Property="VerticalOffset"
                Value="-30" />

        <Setter Property="HorizontalAlignment"
                Value="Center" />

        <Setter Property="Height"
                Value="35" />

        <Setter Property="Placement"
                Value="Top" />

    </Style>
    
    <!--// Popup Border //-->

    <Style x:Key="border"
           TargetType="Border">

        <Setter Property="Height"
                Value="27" />

        <Setter Property="Background"
                Value="#FFFFFF" />

        <Setter Property="CornerRadius"
                Value="4" />

        <Setter Property="VerticalAlignment"
                Value="Top" />

    </Style>
    
    <!--// Popup Text //-->

    <Style x:Key="PopupText"
           TargetType="TextBlock">

        <Setter Property="Foreground"
                Value="#000000" />

        <Setter Property="FontFamily"
                Value="Roboto" />

        <Setter Property="FontSize"
                Value="16" />

        <Setter Property="VerticalAlignment"
                Value="Center" />

        <Setter Property="HorizontalAlignment"
                Value="Center" />

    </Style>

    <!--// Dock Bar Popup Arrow Path //-->

    <Style x:Key="ArrowPath"
           TargetType="Path">

        <Setter Property="HorizontalAlignment"
                Value="Center" />

        <Setter Property="VerticalAlignment"
                Value="Bottom" />

        <Setter Property="Data"
                Value="M0,0 L8,8 16,0 L0,0" />

        <Setter Property="Fill"
                Value="#FFFFFF" />

        <Setter Property="Height"
                Value="8" />

        <Setter Property="Width"
                Value="16" />

        <Setter Property="Stroke"
                Value="#FFFFFF" />

        <Setter Property="Stretch"
                Value="None" />

    </Style>


    <!--// User Style //-->

    <Style x:Key="UserStyle"
           TargetType="Button">

        <Setter Property="Height"
                Value="40" />

        <Setter Property="Width"
                Value="40" />

        <Setter Property="Template">

            <Setter.Value>

                <ControlTemplate TargetType="{x:Type Button}">

                    <Border x:Name="border"
                            BorderThickness="0"
                            Background="{TemplateBinding Background}" />

                </ControlTemplate>

            </Setter.Value>

        </Setter>

        <Style.Triggers>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="(Button.Width)"
                                         To="45"
                                         Duration="0:0:0.3" />

                        <DoubleAnimation Storyboard.TargetProperty="(Button.Height)"
                                         To="45"
                                         Duration="0:0:0.3" />

                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>

            <EventTrigger RoutedEvent="MouseLeave">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="(Button.Width)"
                                         To="40"
                                         Duration="0:0:0.3" />

                        <DoubleAnimation Storyboard.TargetProperty="(Button.Height)"
                                         To="40"
                                         Duration="0:0:0.3" />

                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="user_popup"
           TargetType="Popup">

        <Setter Property="AllowsTransparency"
                Value="True" />

        <Setter Property="VerticalOffset"
                Value="12" />

        <Setter Property="VerticalAlignment"
                Value="Bottom" />


        <Setter Property="Height"
                Value="35" />

        <Setter Property="Placement"
                Value="Bottom" />

    </Style>

    <Style x:Key="user_border"
           TargetType="Border">

        <Setter Property="Height"
                Value="27" />

        <Setter Property="Background"
                Value="#FFFFFF" />

        <Setter Property="CornerRadius"
                Value="4" />

        <Setter Property="VerticalAlignment"
                Value="Bottom" />

    </Style>

    <Style x:Key="user_arrow_path"
           TargetType="Path">

        <Setter Property="HorizontalAlignment"
                Value="Right" />

        <Setter Property="VerticalAlignment"
                Value="Top" />

        <Setter Property="Data"
                Value="M0,8 L0,8 L8,0 L16,8 L0,8" />

        <Setter Property="Fill"
                Value="#FFFFFF" />

        <Setter Property="Height"
                Value="8" />

        <Setter Property="Width"
                Value="16" />

        <Setter Property="Stroke"
                Value="#FFFFFF" />

        <Setter Property="Stretch"
                Value="None" />

        <Setter Property="Margin"
                Value="0,0,15,0" />

    </Style>

    <!--// Close Button Style //-->

    <ImageBrush x:Key="close_def"
                ImageSource="/Assets/shutdown_def.png"
                Stretch="Uniform" />

    <ImageBrush x:Key="close_mo"
                ImageSource="/Assets/shutdown_mo.png"
                Stretch="Uniform" />

    <Style x:Key="CloseBtnStyle"
           TargetType="{x:Type Button}">

        <Setter Property="Height"
                Value="30" />
        <Setter Property="Width"
                Value="30" />
        <Setter Property="HorizontalAlignment"
                Value="Right" />
        <Setter Property="VerticalAlignment"
                Value="Top" />
        <Setter Property="Margin"
                Value="0,40,35,0" />

        <Setter Property="Template">

            <Setter.Value>

                <ControlTemplate TargetType="{x:Type Button}">

                    <Border x:Name="border"
                            BorderThickness="0"
                            Background="{StaticResource close_def}" />

                    <ControlTemplate.Triggers>

                        <Trigger Property="IsMouseOver"
                                 Value="True">

                            <Setter Property="Background"
                                    TargetName="border"
                                    Value="{StaticResource close_mo}" />

                        </Trigger>

                        <Trigger Property="IsPressed"
                                 Value="True">

                            <Setter Property="Background"
                                    TargetName="border"
                                    Value="{StaticResource close_mo}" />

                        </Trigger>

                    </ControlTemplate.Triggers>

                </ControlTemplate>

            </Setter.Value>

        </Setter>

    </Style>



</ResourceDictionary>