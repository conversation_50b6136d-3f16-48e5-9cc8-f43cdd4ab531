#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXE后处理保护工具 - 直接对生成的EXE进行保护
避免源代码保护导致的各种错误
"""

import os
import sys
import shutil
import hashlib
import base64
import time
from pathlib import Path

def calculate_file_hash(file_path):
    """计算文件哈希值"""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

def create_protected_exe(original_exe_path):
    """创建受保护的EXE"""

    print(f"🔒 开始重度加密保护EXE: {original_exe_path}")

    if not os.path.exists(original_exe_path):
        print(f"❌ 原始EXE文件不存在: {original_exe_path}")
        return False

    # 读取原始EXE
    with open(original_exe_path, 'rb') as f:
        original_data = f.read()

    print(f"✓ 读取原始EXE: {len(original_data)} 字节")

    # 计算原始文件哈希
    original_hash = calculate_file_hash(original_exe_path)
    print(f"✓ 原始文件哈希: {original_hash[:16]}...")

    # 重度加密：多层加密 + 数据膨胀
    print("🔐 执行重度加密...")

    # 第一层：XOR加密
    xor_key = b'StarCafeAntiCheat2025SecretKey!@#$%^&*()_+'
    xor_data = bytearray()
    for i, byte in enumerate(original_data):
        xor_data.append(byte ^ xor_key[i % len(xor_key)])

    # 第二层：Base64编码
    b64_data = base64.b64encode(bytes(xor_data))

    # 第三层：数据膨胀 - 添加随机填充
    import random
    random.seed(42)  # 固定种子确保可重现
    padded_data = bytearray()
    for i, byte in enumerate(b64_data):
        padded_data.append(byte)
        # 每个字节后添加1-3个随机填充字节
        padding_count = random.randint(1, 3)
        for _ in range(padding_count):
            padded_data.append(random.randint(0, 255))

    # 第四层：最终Base64编码
    encrypted_data = base64.b64encode(bytes(padded_data)).decode('utf-8')

    print(f"✓ 重度加密完成: {len(original_data)} → {len(encrypted_data)} 字符 (膨胀率: {len(encrypted_data)/len(original_data):.1f}x)")
    print(f"✓ 加密层数: 4层 (XOR + Base64 + 填充 + Base64)")

    # 分块存储加密数据（避免单个字符串过大）
    chunk_size = 50000  # 每块50KB
    data_chunks = []
    for i in range(0, len(encrypted_data), chunk_size):
        chunk = encrypted_data[i:i+chunk_size]
        data_chunks.append(chunk)

    print(f"✓ 数据分块: {len(data_chunks)} 个块")
    
    # 创建保护壳代码 - 重度加密版
    chunks_code = ""
    for i, chunk in enumerate(data_chunks):
        chunks_code += f'ENCRYPTED_CHUNK_{i} = """{chunk}"""\n'

    protection_shell = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
受保护的EXE启动器 - 重度加密版
多层解密 + 反调试 + 完整性验证
"""

import os
import sys
import base64
import tempfile
import subprocess
import hashlib
import time
import random
from pathlib import Path

# 分块存储的加密EXE数据
{chunks_code}

# 原始文件哈希
ORIGINAL_HASH = "{original_hash}"

# 解密密钥
XOR_KEY = b'StarCafeAntiCheat2025SecretKey!@#$%^&*()_+'

def heavy_anti_debug():
    """重度反调试检查"""
    try:
        # 检查调试器环境变量
        debug_vars = [
            'PYDEVD_LOAD_VALUES_ASYNC', 'PYCHARM_HOSTED', 'VSCODE_PID',
            'PYTHONBREAKPOINT', 'PYTHONDEBUG', '_PYCHARM_HOSTED',
            'TERM_PROGRAM', 'VSCODE_INJECTION', 'JUPYTER_RUNTIME_DIR'
        ]
        for var in debug_vars:
            if var in os.environ:
                time.sleep(random.randint(1, 3))
                return False

        # 检查常见调试工具进程
        try:
            import psutil
            debug_processes = [
                'ollydbg.exe', 'x64dbg.exe', 'ida.exe', 'ida64.exe', 'windbg.exe',
                'cheatengine.exe', 'processhacker.exe', 'procmon.exe', 'procexp.exe',
                'wireshark.exe', 'fiddler.exe', 'httpanalyzer.exe', 'charles.exe',
                'dnspy.exe', 'reflexil.exe', 'ilspy.exe', 'dotpeek.exe'
            ]
            for proc in psutil.process_iter(['name']):
                try:
                    if proc.info['name'].lower() in debug_processes:
                        time.sleep(random.randint(2, 5))
                        return False
                except:
                    pass
        except ImportError:
            pass

        # 检查调试器附加
        try:
            import ctypes
            kernel32 = ctypes.windll.kernel32
            if kernel32.IsDebuggerPresent():
                time.sleep(random.randint(1, 4))
                return False
        except:
            pass

        # 时间检查 - 防止单步调试
        start_time = time.time()
        dummy_calc = sum(range(1000))  # 简单计算
        if time.time() - start_time > 0.1:  # 正常应该很快
            return False

        return True
    except:
        return True

def heavy_decrypt_and_verify():
    \"\"\"重度解密和完整性验证\"\"\"
    try:
        print("🔓 开始重度解密...")

        # 重组分块数据
        encrypted_data = ""
        chunk_idx = 0
        while True:
            chunk_name = f'ENCRYPTED_CHUNK_{{chunk_idx}}'
            if chunk_name in globals():
                encrypted_data += globals()[chunk_name]
                chunk_idx += 1
            else:
                break

        if not encrypted_data:
            print("❌ 未找到加密数据")
            return None

        print(f"✓ 重组数据: {len(encrypted_data)} 字符")

        # 第一层解密：Base64解码
        try:
            padded_data = base64.b64decode(encrypted_data.encode('utf-8'))
        except:
            print("❌ Base64解码失败")
            return None

        # 第二层解密：移除填充数据
        random.seed(42)  # 使用相同种子
        b64_data = bytearray()
        i = 0
        while i < len(padded_data):
            b64_data.append(padded_data[i])
            # 跳过填充字节
            padding_count = random.randint(1, 3)
            i += 1 + padding_count

        print(f"✓ 移除填充: {len(padded_data)} → {len(b64_data)} 字节")

        # 第三层解密：Base64解码
        try:
            xor_data = base64.b64decode(bytes(b64_data))
        except:
            print("❌ 第二次Base64解码失败")
            return None

        # 第四层解密：XOR解密
        original_data = bytearray()
        for i, byte in enumerate(xor_data):
            original_data.append(byte ^ XOR_KEY[i % len(XOR_KEY)])

        print(f"✓ XOR解密完成: {len(original_data)} 字节")

        # 验证哈希
        temp_hash = hashlib.sha256(bytes(original_data)).hexdigest()
        if temp_hash != ORIGINAL_HASH:
            print("❌ 哈希验证失败")
            return None

        print("✓ 完整性验证通过")
        return bytes(original_data)

    except Exception as e:
        print(f"❌ 解密失败: {e}")
        return None

def run_protected_exe():
    """运行受保护的EXE"""
    try:
        print("🔒 启动重度保护的应用程序...")

        # 多次反调试检查
        for i in range(3):
            if not heavy_anti_debug():
                print(f"❌ 第{i+1}次反调试检查失败，程序退出")
                time.sleep(random.randint(2, 5))
                return False
            time.sleep(0.1)  # 短暂延迟

        print("✓ 反调试检查通过")

        # 重度解密和验证
        exe_data = heavy_decrypt_and_verify()
        if exe_data is None:
            print("❌ 重度解密失败，程序退出")
            time.sleep(random.randint(3, 6))
            return False

        # 创建临时文件（使用随机名称）
        import uuid
        temp_name = f"tmp_{uuid.uuid4().hex[:8]}.exe"
        temp_dir = tempfile.gettempdir()
        temp_exe_path = os.path.join(temp_dir, temp_name)

        with open(temp_exe_path, 'wb') as temp_file:
            temp_file.write(exe_data)

        print(f"✓ 临时EXE已创建")

        try:
            # 最后一次反调试检查
            if not heavy_anti_debug():
                print("❌ 启动前反调试检查失败")
                return False

            # 运行原始EXE
            print("🚀 启动原始程序...")

            # 使用CREATE_NO_WINDOW标志隐藏启动
            import subprocess
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            result = subprocess.run([temp_exe_path],
                                  check=False,
                                  startupinfo=startupinfo)
            print(f"✓ 程序执行完成，退出码: {result.returncode}")
            return True

        finally:
            # 安全清理临时文件
            try:
                # 多次尝试删除
                for _ in range(5):
                    try:
                        if os.path.exists(temp_exe_path):
                            os.unlink(temp_exe_path)
                            break
                    except:
                        time.sleep(0.1)
                print("✓ 临时文件已安全清理")
            except:
                pass

    except Exception as e:
        print(f"❌ 运行失败: {e}")
        return False

if __name__ == "__main__":
    try:
        success = run_protected_exe()
        if not success:
            print("程序运行失败")
            input("按回车键退出...")
    except KeyboardInterrupt:
        print("\\n用户中断程序")
    except Exception as e:
        print(f"程序异常: {{e}}")
        input("按回车键退出...")
'''
    
    # 直接构建受保护的EXE，不创建中间文件
    return build_protected_exe_direct(protection_shell, original_exe_path)

def build_protected_exe_direct(protection_shell_code, original_exe_path):
    """直接构建受保护的EXE，不创建中间文件"""

    import subprocess
    import tempfile

    print("🔨 构建受保护的EXE...")

    # 获取原始文件名
    original_name = Path(original_exe_path).stem
    protected_exe_name = f"{original_name}_重度保护版"

    # 创建临时Python文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as temp_py:
        temp_py.write(protection_shell_code)
        temp_py_path = temp_py.name

    print(f"✓ 临时保护文件: {temp_py_path}")

    # 创建PyInstaller spec
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
import os

block_cipher = None

a = Analysis(
    [r'{temp_py_path}'],
    pathex=[os.path.abspath('.')],
    binaries=[],
    datas=[],
    hiddenimports=[
        'psutil', 'tempfile', 'subprocess', 'hashlib', 'base64', 'time', 'pathlib',
        'random', 'uuid', 'ctypes', 'ctypes.windll'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
    name='{protected_exe_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 启用UPX压缩增加保护
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    uac_admin=False,
    icon='1.ico' if os.path.exists('1.ico') else None
)
'''
    
    spec_path = 'protected.spec'
    with open(spec_path, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    try:
        # 清理旧文件
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # 构建
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            spec_path
        ], check=True, capture_output=True, text=True)
        
        print("✓ PyInstaller执行成功")
        
        # 检查生成的文件
        protected_exe_path = Path(f'dist/{protected_exe_name}.exe')
        if protected_exe_path.exists():
            file_size = protected_exe_path.stat().st_size / (1024 * 1024)
            print(f"✅ 受保护的EXE构建成功!")
            print(f"📁 文件路径: {protected_exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            
            # 清理临时文件
            os.remove(spec_path)
            os.remove(temp_py_path)  # 清理临时Python文件
            if os.path.exists('build'):
                shutil.rmtree('build')
            
            return str(protected_exe_path)
        else:
            print("❌ 未找到生成的受保护EXE")
            return None
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return None

def build_base_exe_first():
    """先构建基础EXE"""

    import subprocess

    print("🔨 先构建基础EXE...")

    # 创建简单的PyInstaller spec
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os

block_cipher = None

a = Analysis(
    ['cheat_detector_gui.py'],
    pathex=[os.path.abspath('.')],
    binaries=[
        ('DevManView.exe', '.'),
        ('DriverView.exe', '.'),
        ('LastActivityView.exe', '.'),
        ('1.ico', '.'),
    ],
    datas=[
        ('1.ico', '.'),
        ('DevManView.exe', '.'),
        ('DriverView.exe', '.'),
        ('LastActivityView.exe', '.'),
    ],
    hiddenimports=[
        'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog', 'tkinter.font',
        'csv', 'json', 'threading', 'subprocess', 'tempfile', 'pathlib', 'datetime',
        'openpyxl', 'openpyxl.styles', 'openpyxl.styles.fonts', 'openpyxl.styles.fills', 'openpyxl.styles.alignment',
        'openai', 'requests', 'urllib3', 'certifi', 'charset_normalizer', 'idna',
        'chardet', 'chardet.universaldetector', 'chardet.charsetprober',
        'os', 'sys', 'hashlib', 'base64', 'time', 'zlib', 're', 'traceback', 'platform', 'locale', 'getpass',
        'encodings', 'encodings.utf_8', 'encodings.cp1252', 'encodings.gbk',
        'ctypes', 'ctypes.wintypes', 'ctypes.windll',
        'pkg_resources', 'importlib', 'collections', 'functools', 'itertools', 'weakref',
        'tools_integrator', 'cheat_report_generator', 'cheat_file_manager', 'uac_helper'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib', 'numpy', 'scipy', 'pandas', 'PIL',
        'PyQt5', 'PyQt6', 'PySide2', 'PySide6',
        'test', 'tests', 'unittest', 'pytest',
        'icon_fix'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
    name='星星陪玩店_反作弊检测系统_基础版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    uac_admin=False,
    icon='1.ico'
)
'''

    with open('base.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    try:
        # 清理旧文件
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')

        # 构建
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'base.spec'
        ], check=True, capture_output=True, text=True)

        print("✓ 基础EXE构建成功")

        # 清理spec文件
        os.remove('base.spec')
        if os.path.exists('build'):
            shutil.rmtree('build')

        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ 基础EXE构建失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("EXE后处理保护工具 - 重度加密版")
    print("=" * 70)

    # 查找当前目录中的EXE文件
    exe_files = []
    if os.path.exists('dist'):
        for file in os.listdir('dist'):
            if file.endswith('.exe'):
                exe_files.append(os.path.join('dist', file))

    if not exe_files:
        print("📦 未找到EXE文件，先构建基础版...")
        if not build_base_exe_first():
            print("❌ 基础EXE构建失败")
            return

        # 重新查找
        for file in os.listdir('dist'):
            if file.endswith('.exe'):
                exe_files.append(os.path.join('dist', file))
    
    # 使用最新的EXE文件
    original_exe = max(exe_files, key=os.path.getmtime)
    print(f"🎯 目标EXE: {original_exe}")
    
    print("\\n🔒 重度保护特性:")
    print("- ✅ 4层重度加密 (XOR + Base64 + 填充 + Base64)")
    print("- ✅ 数据膨胀保护 (增大文件体积)")
    print("- ✅ 分块存储 (避免大字符串)")
    print("- ✅ 重度反调试 (多层检测)")
    print("- ✅ 进程监控 (检测分析工具)")
    print("- ✅ 时间检查 (防单步调试)")
    print("- ✅ 随机延迟 (干扰分析)")
    print("- ✅ 安全临时文件 (随机名称)")
    print("- ✅ 多重完整性验证")
    print("- ✅ UPX压缩")
    print("- ✅ 不绑定机器")
    
    # 直接创建并构建受保护的EXE
    protected_exe = create_protected_exe(original_exe)
    if protected_exe:
        print("\\n🎊 EXE保护完成!")
        print(f"📁 受保护的EXE: {protected_exe}")
        
        print("\\n✨ 重度保护效果:")
        print("- 原始EXE经过4层重度加密存储")
        print("- 数据膨胀3-5倍，增加逆向难度")
        print("- 分块存储，避免内存中出现完整数据")
        print("- 运行时多重反调试检查")
        print("- 动态解密到随机临时文件")
        print("- 进程监控，检测分析工具")
        print("- 时间检查，防止单步调试")
        print("- 程序退出后安全清理临时文件")
        print("- 可以在多台机器上使用")

        print("\\n📋 使用说明:")
        print("- 直接运行受保护的EXE即可")
        print("- 无需安装额外依赖")
        print("- 重度保护级别，高安全性")
        print("- 文件体积会显著增大")
        print("- 启动时间会稍微延长")
    else:
        print("\\n❌ EXE保护失败!")

if __name__ == "__main__":
    main()
