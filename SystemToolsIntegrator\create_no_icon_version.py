#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建完全不使用icon_fix的版本
"""

import os
import shutil

def create_no_icon_main():
    """创建不使用icon_fix的主程序"""
    
    print("🔧 创建不使用icon_fix的主程序...")
    
    main_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
星星陪玩店 - 选手作弊检测系统 (主程序) - 无图标版
完全不使用icon_fix模块，避免测试窗口
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """主函数"""
    try:
        print("=" * 60)
        print("星星陪玩店 - 陪玩作弊检测系统")
        print("=" * 60)
        print("技术部内部工具")
        print("时间: 2025-01-27")
        print("版本: v4.5 (无图标纯净版)")
        print("=" * 60)
        
        # 检查权限状态（但不强制提升）
        try:
            from uac_helper import is_admin, get_privilege_status
            status = get_privilege_status()
            print(f"当前权限状态: {status}")
            if not is_admin():
                print("提示: 建议以管理员权限运行以获得完整功能")
            else:
                print("✓ 已具有管理员权限")
        except ImportError:
            print("警告: UAC助手模块未找到，继续运行...")
        
        # 完全跳过图标处理
        print("✓ 跳过图标处理 (避免测试窗口)")
        
        # 导入并创建作弊检测GUI
        print("正在启动作弊检测系统...")
        print("🔧 步骤1: 导入GUI模块...")
        from cheat_detector_gui import CheatDetectorGUI
        print("✓ GUI模块导入成功")

        print("🔧 步骤2: 创建GUI实例...")
        app = CheatDetectorGUI()
        print("✓ GUI实例创建成功")
        
        # 完全跳过图标应用
        print("✓ 跳过图标应用 (避免测试窗口)")
        
        print("✓ 作弊检测系统已启动")
        print("✓ 支持硬件指纹检测")
        print("✓ 支持驱动完整性验证")
        print("✓ 支持行为模式分析")
        print("✓ 生成详细检测报告")
        print()
        
        # 运行GUI
        print("🔧 步骤3: 开始运行GUI...")
        app.run()
        print("✓ GUI运行完成")
        
        print("作弊检测系统已正常退出")
        return True
        
    except ImportError as e:
        error_msg = f"导入模块失败: {e}"
        print(f"❌ {error_msg}")
        return False
        
    except Exception as e:
        error_msg = f"程序运行错误: {e}"
        print(f"❌ {error_msg}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("程序启动失败")
    except Exception as e:
        print(f"程序异常退出: {e}")
        import traceback
        traceback.print_exc()
'''
    
    with open('cheat_detector_main_no_icon.py', 'w', encoding='utf-8') as f:
        f.write(main_content)
    
    print("✅ 无图标主程序创建完成")
    return True

def create_no_icon_spec():
    """创建不使用icon_fix的spec文件"""
    
    print("📝 创建不使用icon_fix的spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# 完全不使用icon_fix的PyInstaller配置

import sys
import os

block_cipher = None

a = Analysis(
    ['cheat_detector_main_no_icon.py'],
    pathex=[os.path.abspath('.')],
    binaries=[
        ('DevManView.exe', '.'),
        ('DriverView.exe', '.'),
        ('LastActivityView.exe', '.'),
        ('1.ico', '.'),
    ],
    datas=[
        ('1.ico', '.'),
        ('DevManView.exe', '.'),
        ('DriverView.exe', '.'),
        ('LastActivityView.exe', '.'),
    ],
    hiddenimports=[
        'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog', 'tkinter.font',
        'csv', 'json', 'threading', 'subprocess', 'tempfile', 'pathlib', 'datetime',
        'openpyxl', 'openpyxl.styles', 'openpyxl.styles.fonts', 'openpyxl.styles.fills', 'openpyxl.styles.alignment',
        'openai', 'requests', 'urllib3', 'certifi', 'charset_normalizer', 'idna',
        'chardet', 'chardet.universaldetector', 'chardet.charsetprober',
        'os', 'sys', 'hashlib', 'base64', 'time', 'zlib', 're', 'traceback', 'platform', 'locale', 'getpass',
        'encodings', 'encodings.utf_8', 'encodings.cp1252', 'encodings.gbk',
        'ctypes', 'ctypes.wintypes', 'ctypes.windll',
        'pkg_resources', 'importlib', 'collections', 'functools', 'itertools', 'weakref',
        'cheat_detector_gui', 'tools_integrator', 'cheat_report_generator', 'cheat_file_manager', 'uac_helper'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib', 'numpy', 'scipy', 'pandas', 'PIL', 
        'PyQt5', 'PyQt6', 'PySide2', 'PySide6',
        'test', 'tests', 'unittest', 'pytest',
        'icon_fix'  # 完全排除icon_fix模块
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
    name='星星陪玩店_反作弊检测系统_无图标纯净版_v4.5',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False, 
    upx=False,
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    uac_admin=False,
    icon='1.ico'
)
'''
    
    with open('no_icon.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 无图标spec文件创建完成")
    return True

def build_no_icon_exe():
    """构建无图标版EXE"""
    
    import subprocess
    import sys
    from pathlib import Path
    
    print("🔨 开始构建无图标版EXE...")
    
    # 清理旧文件
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("✓ 清理build目录")
    
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("✓ 清理dist目录")
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'no_icon.spec'
        ], check=True, capture_output=True, text=True)
        
        print("✓ PyInstaller执行成功")
        
        # 检查生成的文件
        exe_path = Path('dist/星星陪玩店_反作弊检测系统_无图标纯净版_v4.5.exe')
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)
            print(f"✅ 无图标纯净版EXE构建成功!")
            print(f"📁 文件路径: {exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            
            print("\n🎉 无图标纯净版特性:")
            print("- ✅ 完全不使用icon_fix模块")
            print("- ✅ 绝对无测试窗口")
            print("- ✅ 修复重复启动Bug")
            print("- ✅ 简洁美观的AI分析按钮")
            print("- ✅ 无控制台专业外观")
            print("- ✅ 所有功能完整")
            
            # 清理临时文件
            os.remove('no_icon.spec')
            if os.path.exists('build'):
                shutil.rmtree('build')
            
            return True
        else:
            print("❌ 未找到生成的exe文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller执行失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("创建完全不使用icon_fix的版本")
    print("=" * 70)
    
    print("🎯 解决方案:")
    print("1. 创建不导入icon_fix的主程序")
    print("2. 在PyInstaller中排除icon_fix模块")
    print("3. 构建完全干净的EXE")
    
    # 步骤1：创建无图标主程序
    if not create_no_icon_main():
        print("❌ 创建主程序失败")
        return
    
    # 步骤2：创建无图标spec
    if not create_no_icon_spec():
        print("❌ 创建spec文件失败")
        return
    
    # 步骤3：构建EXE
    if build_no_icon_exe():
        print("\n🎊 恭喜！无图标纯净版EXE构建完成！")
        print("这个版本完全不使用icon_fix，绝对不会有测试窗口！")
        
        print(f"\n📁 无图标纯净版EXE位置:")
        print(f"dist\\星星陪玩店_反作弊检测系统_无图标纯净版_v4.5.exe")
        
        # 自动清理临时文件
        temp_files = ['cheat_detector_main_no_icon.py', 'cleanup_directory.py']
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                os.remove(temp_file)
                print(f"✓ 自动清理: {temp_file}")
        
        print("\n✅ 目录已自动清理，只保留核心文件和最终EXE")
    else:
        print("\n❌ 构建失败!")

if __name__ == "__main__":
    main()
