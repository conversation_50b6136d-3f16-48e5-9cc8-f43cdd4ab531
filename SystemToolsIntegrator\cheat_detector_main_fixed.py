#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
星星陪玩店 - 选手作弊检测系统 (主程序) - 修复版
跳过UAC权限检查，避免闪退问题
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """主函数"""
    try:
        print("=" * 60)
        print("星星陪玩店 - 陪玩作弊检测系统")
        print("=" * 60)
        print("技术部内部工具")
        print("时间: 2025-01-27")
        print("版本: v0.2 (作弊检测专版)")
        print("=" * 60)
        
        # 检查权限状态（但不强制提升）
        try:
            from uac_helper import is_admin, get_privilege_status
            status = get_privilege_status()
            print(f"当前权限状态: {status}")
            if not is_admin():
                print("提示: 建议以管理员权限运行以获得完整功能")
            else:
                print("✓ 已具有管理员权限")
        except ImportError:
            print("警告: UAC助手模块未找到，继续运行...")
        
        # 确保图标文件存在
        try:
            from icon_fix import fix_window_icon, create_icon_if_missing
            icon_path = create_icon_if_missing()
            if icon_path:
                print(f"✓ 图标文件: {icon_path}")
        except ImportError:
            print("警告: 图标修复模块未找到")
            icon_path = None
        
        # 导入并创建作弊检测GUI
        print("正在启动作弊检测系统...")
        print("🔧 步骤1: 导入GUI模块...")
        from cheat_detector_gui import CheatDetectorGUI
        print("✓ GUI模块导入成功")

        print("🔧 步骤2: 创建GUI实例...")
        app = CheatDetectorGUI()
        print("✓ GUI实例创建成功")
        
        # 应用图标修复
        if icon_path:
            try:
                fix_window_icon(app.root, icon_path)
                print("✓ 图标应用成功")
            except:
                print("警告: 图标应用失败")
        
        print("✓ 作弊检测系统已启动")
        print("✓ 支持硬件指纹检测")
        print("✓ 支持驱动完整性验证")
        print("✓ 支持行为模式分析")
        print("✓ 生成详细检测报告")
        print()
        
        # 运行GUI
        print("🔧 步骤3: 开始运行GUI...")

        # 添加强制保持运行的机制
        try:
            app.run()
            print("✓ GUI运行完成")
        except Exception as e:
            print(f"❌ GUI运行出错: {e}")
            import traceback
            traceback.print_exc()

            # 强制保持程序运行
            print("🔧 尝试强制保持程序运行...")
            try:
                import time
                while True:
                    print("⏰ 程序保持运行中... (按Ctrl+C退出)")
                    time.sleep(5)
            except KeyboardInterrupt:
                print("✓ 用户手动退出程序")
            except Exception as e2:
                print(f"❌ 强制保持运行也失败: {e2}")
        
        print("作弊检测系统已正常退出")
        return True
        
    except ImportError as e:
        error_msg = f"导入模块失败: {e}"
        print(f"❌ {error_msg}")
        
        # 显示错误对话框
        try:
            import tkinter as tk
            import tkinter.messagebox as msgbox
            root = tk.Tk()
            root.withdraw()
            msgbox.showerror("启动失败", f"作弊检测系统启动失败:\n{error_msg}\n\n请检查程序文件是否完整")
            root.destroy()
        except:
            pass
        
        return False
        
    except Exception as e:
        error_msg = f"程序运行错误: {e}"
        print(f"❌ {error_msg}")
        
        # 显示错误对话框
        try:
            import tkinter as tk
            import tkinter.messagebox as msgbox
            root = tk.Tk()
            root.withdraw()
            msgbox.showerror("运行错误", f"作弊检测系统运行时出现错误:\n{error_msg}")
            root.destroy()
        except:
            pass
        
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("程序启动失败")
    except Exception as e:
        print(f"程序异常退出: {e}")
        import traceback
        traceback.print_exc()
    
    # 在exe环境中保持控制台窗口（仅在有控制台时）
    if getattr(sys, 'frozen', False):
        try:
            # 检查是否有控制台
            if hasattr(sys.stdin, 'read'):
                input("\n按回车键退出...")
        except:
            # 无控制台模式，直接退出
            pass
