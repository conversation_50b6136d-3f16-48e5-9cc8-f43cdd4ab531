#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
作弊检测报告生成器
隐去真实工具信息，使用作弊检测术语
"""

import datetime
import csv
import json
from pathlib import Path

class CheatReportGenerator:
    def __init__(self):
        pass
        
    def generate_all_reports(self, data, output_dir):
        """生成所有作弊检测报告"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            
            results = []
            
            # 生成硬件指纹分析报告
            hardware_csv = output_path / f"硬件指纹分析报告_{timestamp}.csv"
            success1 = self.generate_hardware_report(data.get('devices', []), hardware_csv)
            results.append(success1)
            
            # 生成驱动完整性验证报告
            driver_csv = output_path / f"驱动完整性验证报告_{timestamp}.csv"
            success2 = self.generate_driver_report(data.get('drivers', []), driver_csv)
            results.append(success2)
            
            # 生成行为模式分析报告
            behavior_csv = output_path / f"行为模式分析报告_{timestamp}.csv"
            success3 = self.generate_behavior_report(data.get('activities', []), behavior_csv)
            results.append(success3)
            
            # 生成综合检测汇总报告
            summary_csv = output_path / f"综合检测汇总报告_{timestamp}.csv"
            success4 = self.generate_summary_report(data, summary_csv)
            results.append(success4)
            
            # 尝试生成Excel完整报告
            try:
                excel_file = output_path / f"作弊检测完整报告_{timestamp}.xlsx"
                success5 = self.generate_excel_report(data, excel_file)
                results.append(success5)
            except ImportError:
                print("警告: 未安装openpyxl，跳过Excel文件生成")
                pass
            
            return all(results)
            
        except Exception as e:
            print(f"生成检测报告时出错: {e}")
            return False
            
    def generate_hardware_report(self, devices, output_path):
        """生成硬件指纹分析报告"""
        try:
            with open(output_path, 'w', newline='', encoding='utf-8-sig') as f:
                if not devices:
                    writer = csv.writer(f)
                    writer.writerow(['提示', '未检测到硬件信息'])
                    return True
                    
                # 获取所有可能的字段
                all_fields = set()
                for device in devices:
                    all_fields.update(device.keys())
                
                # 设备字段排序 - 完整字段映射
                common_fields = ['device_name', 'device_id', 'hardware_id', 'compatible_id', 'instance_id',
                               'service_name', 'problem_code', 'problem_status', 'disabled', 'connected',
                               'device_manufacturer', 'device_description', 'device_type', 'device_location',
                               'driver_description', 'driver_version', 'driver_date', 'driver_company']
                other_fields = sorted(all_fields - set(common_fields))
                fieldnames = [f for f in common_fields if f in all_fields] + other_fields

                writer = csv.DictWriter(f, fieldnames=fieldnames)

                # 写入中文表头（作弊检测术语）
                header_map = {
                    'device_name': '设备名称',
                    'device_id': '设备ID',
                    'hardware_id': '硬件ID',
                    'compatible_id': '兼容ID',
                    'instance_id': '实例ID',
                    'service_name': '服务名称',
                    'problem_code': '问题代码',
                    'problem_status': '问题状态',
                    'disabled': '是否禁用',
                    'connected': '连接状态',
                    'device_manufacturer': '设备制造商',
                    'device_description': '设备描述',
                    'device_type': '设备类型',
                    'device_location': '设备位置',
                    'driver_description': '驱动描述',
                    'driver_version': '驱动版本',
                    'driver_date': '驱动日期',
                    'driver_company': '驱动公司',
                    'install_time': '安装时间',
                    'first_install_time': '首次安装时间',
                    'last_arrival_time': '最后连接时间'
                }
                
                chinese_header = {}
                for field in fieldnames:
                    chinese_header[field] = header_map.get(field, field)
                
                writer.writerow(chinese_header)
                writer.writerows(devices)
                
            print(f"✓ 硬件指纹分析报告已生成: {output_path}")
            return True
            
        except Exception as e:
            print(f"生成硬件报告时出错: {e}")
            return False
            
    def generate_driver_report(self, drivers, output_path):
        """生成驱动完整性验证报告"""
        try:
            with open(output_path, 'w', newline='', encoding='utf-8-sig') as f:
                if not drivers:
                    writer = csv.writer(f)
                    writer.writerow(['提示', '未检测到驱动程序信息'])
                    return True
                    
                # 获取所有可能的字段
                all_fields = set()
                for driver in drivers:
                    all_fields.update(driver.keys())
                
                # 驱动字段排序 - 完整字段映射
                common_fields = ['filename', 'base_address', 'end_address', 'size', 'load_count',
                               'driver_type', 'description', 'version', 'company', 'product_name',
                               'file_created_time', 'file_modified_time', 'full_path', 'digital_signature']
                other_fields = sorted(all_fields - set(common_fields))
                fieldnames = [f for f in common_fields if f in all_fields] + other_fields

                writer = csv.DictWriter(f, fieldnames=fieldnames)

                # 写入中文表头（作弊检测术语）
                header_map = {
                    'filename': '驱动文件名',
                    'base_address': '基地址',
                    'end_address': '结束地址',
                    'size': '文件大小',
                    'load_count': '加载计数',
                    'load_order_group': '加载顺序组',
                    'driver_type': '驱动类型',
                    'description': '驱动描述',
                    'version': '版本信息',
                    'company': '开发公司',
                    'product_name': '产品名称',
                    'file_created_time': '文件创建时间',
                    'file_modified_time': '文件修改时间',
                    'full_path': '完整路径',
                    'file_attributes': '文件属性',
                    'digital_signature': '数字签名',
                    'certificate_issuer': '证书颁发者',
                    'certificate_subject': '证书主题'
                }
                
                chinese_header = {}
                for field in fieldnames:
                    chinese_header[field] = header_map.get(field, field)
                
                writer.writerow(chinese_header)
                writer.writerows(drivers)
                
            print(f"✓ 驱动完整性验证报告已生成: {output_path}")
            return True
            
        except Exception as e:
            print(f"生成驱动报告时出错: {e}")
            return False
            
    def generate_behavior_report(self, activities, output_path):
        """生成行为模式分析报告"""
        try:
            with open(output_path, 'w', newline='', encoding='utf-8-sig') as f:
                if not activities:
                    writer = csv.writer(f)
                    writer.writerow(['提示', '未检测到行为记录'])
                    return True
                    
                # 获取所有可能的字段
                all_fields = set()
                for activity in activities:
                    all_fields.update(activity.keys())
                
                # 活动字段排序 - 完整字段映射
                common_fields = ['filename', 'extension', 'full_path', 'action_time', 'action_type',
                               'process_name', 'process_id', 'user_name', 'file_size', 'file_attributes',
                               'file_created_time', 'file_modified_time', 'file_accessed_time', 'more_information']
                other_fields = sorted(all_fields - set(common_fields))
                fieldnames = [f for f in common_fields if f in all_fields] + other_fields

                writer = csv.DictWriter(f, fieldnames=fieldnames)

                # 写入中文表头（作弊检测术语）
                header_map = {
                    'filename': '文件名',
                    'extension': '文件扩展名',
                    'full_path': '完整路径',
                    'action_time': '操作时间',
                    'action_type': '操作类型',
                    'process_name': '进程名称',
                    'process_id': '进程ID',
                    'user_name': '用户名',
                    'file_size': '文件大小',
                    'file_attributes': '文件属性',
                    'file_created_time': '文件创建时间',
                    'file_modified_time': '文件修改时间',
                    'file_accessed_time': '文件访问时间',
                    'more_information': '附加信息'
                }
                
                chinese_header = {}
                for field in fieldnames:
                    chinese_header[field] = header_map.get(field, field)
                
                writer.writerow(chinese_header)
                writer.writerows(activities)
                
            print(f"✓ 行为模式分析报告已生成: {output_path}")
            return True
            
        except Exception as e:
            print(f"生成行为报告时出错: {e}")
            return False
            
    def generate_summary_report(self, data, output_path):
        """生成综合检测汇总报告"""
        try:
            import platform
            import socket
            
            with open(output_path, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                
                # 基本信息
                writer.writerow(['星星陪玩店 - 选手作弊检测汇总报告'])
                writer.writerow(['检测时间', datetime.datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")])
                writer.writerow(['目标主机', socket.gethostname()])
                writer.writerow(['系统环境', f"{platform.system()} {platform.release()} {platform.machine()}"])
                writer.writerow(['检测工具', '星星陪玩店作弊检测系统 v2.0'])
                writer.writerow([])
                
                # 检测统计
                writer.writerow(['检测统计信息'])
                writer.writerow(['检测项目', '检测数量'])
                writer.writerow(['硬件组件数量', len(data.get('devices', []))])
                writer.writerow(['驱动程序数量', len(data.get('drivers', []))])
                writer.writerow(['行为记录数量', len(data.get('activities', []))])
                writer.writerow([])
                
                # 风险评估
                device_count = len(data.get('devices', []))
                driver_count = len(data.get('drivers', []))
                activity_count = len(data.get('activities', []))
                
                writer.writerow(['风险评估'])
                writer.writerow(['评估项目', '风险等级'])
                
                # 简单的风险评估逻辑
                if device_count > 50:
                    writer.writerow(['硬件复杂度', '中等风险'])
                else:
                    writer.writerow(['硬件复杂度', '低风险'])
                    
                if driver_count > 100:
                    writer.writerow(['驱动程序数量', '需要关注'])
                else:
                    writer.writerow(['驱动程序数量', '正常范围'])
                    
                if activity_count > 1000:
                    writer.writerow(['系统活跃度', '高活跃'])
                else:
                    writer.writerow(['系统活跃度', '正常'])
                
                writer.writerow([])
                
                # 报告说明
                writer.writerow(['报告文件说明'])
                writer.writerow(['文件类型', '说明'])
                writer.writerow(['硬件指纹分析报告.csv', '包含所有硬件组件的详细信息'])
                writer.writerow(['驱动完整性验证报告.csv', '包含所有驱动程序的完整性信息'])
                writer.writerow(['行为模式分析报告.csv', '包含系统最近的行为活动记录'])
                writer.writerow(['作弊检测完整报告.xlsx', 'Excel格式的完整检测报告'])
                
            print(f"✓ 综合检测汇总报告已生成: {output_path}")
            return True
            
        except Exception as e:
            print(f"生成汇总报告时出错: {e}")
            return False
            
    def generate_excel_report(self, data, output_path):
        """生成Excel检测报告"""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Alignment
            
            # 创建工作簿
            wb = openpyxl.Workbook()
            
            # 删除默认工作表
            wb.remove(wb.active)
            
            # 样式定义
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="E74C3C", end_color="E74C3C", fill_type="solid")
            center_alignment = Alignment(horizontal="center", vertical="center")
            
            # 创建汇总工作表
            summary_ws = wb.create_sheet("检测汇总")
            self._create_summary_sheet(summary_ws, data, header_font, header_fill, center_alignment)
            
            # 创建硬件指纹工作表
            hardware_ws = wb.create_sheet("硬件指纹分析")
            self._create_hardware_sheet(hardware_ws, data.get('devices', []), header_font, header_fill)
            
            # 创建驱动完整性工作表
            driver_ws = wb.create_sheet("驱动完整性验证")
            self._create_driver_sheet(driver_ws, data.get('drivers', []), header_font, header_fill)
            
            # 创建行为模式工作表
            behavior_ws = wb.create_sheet("行为模式分析")
            self._create_behavior_sheet(behavior_ws, data.get('activities', []), header_font, header_fill)
            
            # 保存文件
            wb.save(output_path)
            print(f"✓ Excel检测报告已生成: {output_path}")
            return True
            
        except ImportError:
            print("警告: 未安装openpyxl库，无法生成Excel文件")
            return False
        except Exception as e:
            print(f"生成Excel报告时出错: {e}")
            return False

    def _create_summary_sheet(self, ws, data, header_font, header_fill, center_alignment):
        """创建检测汇总工作表"""
        import platform
        import socket
        from openpyxl.styles import Font

        # 标题
        ws['A1'] = "星星陪玩店 - 选手作弊检测汇总报告"
        ws['A1'].font = Font(size=16, bold=True)
        ws.merge_cells('A1:B1')

        # 基本信息
        row = 3
        ws[f'A{row}'] = "检测时间"
        ws[f'B{row}'] = datetime.datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")
        row += 1

        ws[f'A{row}'] = "目标主机"
        ws[f'B{row}'] = socket.gethostname()
        row += 1

        ws[f'A{row}'] = "系统环境"
        ws[f'B{row}'] = f"{platform.system()} {platform.release()} {platform.machine()}"
        row += 1

        ws[f'A{row}'] = "检测工具"
        ws[f'B{row}'] = "星星陪玩店作弊检测系统 v2.0"
        row += 2

        # 检测统计
        ws[f'A{row}'] = "检测统计信息"
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        ws[f'A{row}'] = "硬件组件数量"
        ws[f'B{row}'] = len(data.get('devices', []))
        row += 1

        ws[f'A{row}'] = "驱动程序数量"
        ws[f'B{row}'] = len(data.get('drivers', []))
        row += 1

        ws[f'A{row}'] = "行为记录数量"
        ws[f'B{row}'] = len(data.get('activities', []))

        # 调整列宽
        ws.column_dimensions['A'].width = 20
        ws.column_dimensions['B'].width = 30

    def _create_hardware_sheet(self, ws, devices, header_font, header_fill):
        """创建硬件指纹分析工作表"""
        if not devices:
            ws['A1'] = "未检测到硬件信息"
            return

        # 表头
        headers = ['硬件组件名称', '制造商标识', '组件类型', '运行状态', '驱动标识']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill

        # 数据
        for row, device in enumerate(devices, 2):
            ws.cell(row=row, column=1, value=device.get('name', ''))
            ws.cell(row=row, column=2, value=device.get('manufacturer', ''))
            ws.cell(row=row, column=3, value=device.get('type', ''))
            ws.cell(row=row, column=4, value=device.get('status', ''))
            ws.cell(row=row, column=5, value=device.get('driver', ''))

        # 调整列宽
        for col in range(1, 6):
            ws.column_dimensions[chr(64 + col)].width = 25

    def _create_driver_sheet(self, ws, drivers, header_font, header_fill):
        """创建驱动完整性验证工作表"""
        if not drivers:
            ws['A1'] = "未检测到驱动程序信息"
            return

        # 表头
        headers = ['驱动程序名称', '版本号', '文件名', '开发商', '安装时间']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill

        # 数据
        for row, driver in enumerate(drivers, 2):
            ws.cell(row=row, column=1, value=driver.get('name', ''))
            ws.cell(row=row, column=2, value=driver.get('version', ''))
            ws.cell(row=row, column=3, value=driver.get('filename', ''))
            ws.cell(row=row, column=4, value=driver.get('manufacturer', ''))
            ws.cell(row=row, column=5, value=driver.get('created', ''))

        # 调整列宽
        for col in range(1, 6):
            ws.column_dimensions[chr(64 + col)].width = 25

    def _create_behavior_sheet(self, ws, activities, header_font, header_fill):
        """创建行为模式分析工作表"""
        if not activities:
            ws['A1'] = "未检测到行为记录"
            return

        # 表头
        headers = ['时间戳', '行为类型', '目标文件', '文件路径', '行为描述']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill

        # 数据 (限制显示前1000条，避免文件过大)
        display_activities = activities[:1000] if len(activities) > 1000 else activities

        for row, activity in enumerate(display_activities, 2):
            ws.cell(row=row, column=1, value=activity.get('time', ''))
            ws.cell(row=row, column=2, value=activity.get('type', ''))
            ws.cell(row=row, column=3, value=activity.get('filename', ''))
            ws.cell(row=row, column=4, value=activity.get('path', ''))
            ws.cell(row=row, column=5, value=activity.get('description', ''))

        # 调整列宽
        ws.column_dimensions['A'].width = 20  # 时间
        ws.column_dimensions['B'].width = 15  # 类型
        ws.column_dimensions['C'].width = 30  # 文件名
        ws.column_dimensions['D'].width = 50  # 路径
        ws.column_dimensions['E'].width = 20  # 描述

        # 如果有超过1000条记录，添加说明
        if len(activities) > 1000:
            note_row = len(display_activities) + 3
            ws.cell(row=note_row, column=1, value=f"注意: 显示前1000条记录，共{len(activities)}条")
