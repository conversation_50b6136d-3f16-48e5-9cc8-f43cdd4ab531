#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
作弊检测文件管理器 - 隐去软件信息
分别保存三个检测模块的详细数据
"""

import os
import json
import csv
import datetime
from pathlib import Path

class CheatFileManager:
    def __init__(self, base_path):
        self.base_path = Path(base_path)
        self.timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建三个检测模块文件夹
        self.hardware_folder = self.base_path / f"硬件指纹检测_{self.timestamp}"
        self.driver_folder = self.base_path / f"驱动完整性验证_{self.timestamp}"
        self.behavior_folder = self.base_path / f"行为模式分析_{self.timestamp}"
        
        # 确保文件夹存在
        self.hardware_folder.mkdir(parents=True, exist_ok=True)
        self.driver_folder.mkdir(parents=True, exist_ok=True)
        self.behavior_folder.mkdir(parents=True, exist_ok=True)
        
    def save_hardware_data(self, devices):
        """保存硬件指纹数据到详细文件夹"""
        try:
            # 保存为JSON格式
            json_file = self.hardware_folder / "硬件指纹数据.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(devices, f, ensure_ascii=False, indent=2)
            
            # 保存为CSV格式
            csv_file = self.hardware_folder / "硬件指纹数据.csv"
            if devices:
                with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                    writer = csv.DictWriter(f, fieldnames=['name', 'manufacturer', 'type', 'status', 'driver'])
                    writer.writeheader()
                    writer.writerows(devices)
            
            # 保存为TXT格式（便于查看）
            txt_file = self.hardware_folder / "硬件指纹汇总.txt"
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("硬件指纹检测汇总报告\n")
                f.write("=" * 60 + "\n")
                f.write(f"检测时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"硬件组件总数: {len(devices)}\n")
                f.write("=" * 60 + "\n\n")
                
                for i, device in enumerate(devices, 1):
                    f.write(f"硬件组件 {i}:\n")
                    f.write(f"  组件名称: {device.get('name', 'N/A')}\n")
                    f.write(f"  制造商: {device.get('manufacturer', 'N/A')}\n")
                    f.write(f"  组件类型: {device.get('type', 'N/A')}\n")
                    f.write(f"  运行状态: {device.get('status', 'N/A')}\n")
                    f.write(f"  驱动标识: {device.get('driver', 'N/A')}\n")
                    f.write("-" * 40 + "\n")
            
            print(f"✓ 硬件指纹数据已保存到: {self.hardware_folder}")
            return True
            
        except Exception as e:
            print(f"保存硬件指纹数据时出错: {e}")
            return False
    
    def save_driver_data(self, drivers):
        """保存驱动完整性数据到详细文件夹"""
        try:
            # 保存为JSON格式
            json_file = self.driver_folder / "驱动完整性数据.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(drivers, f, ensure_ascii=False, indent=2)
            
            # 保存为CSV格式
            csv_file = self.driver_folder / "驱动完整性数据.csv"
            if drivers:
                with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                    writer = csv.DictWriter(f, fieldnames=['name', 'version', 'filename', 'manufacturer', 'created'])
                    writer.writeheader()
                    writer.writerows(drivers)
            
            # 保存为TXT格式（便于查看）
            txt_file = self.driver_folder / "驱动完整性汇总.txt"
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("驱动完整性验证汇总报告\n")
                f.write("=" * 60 + "\n")
                f.write(f"检测时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"驱动程序总数: {len(drivers)}\n")
                f.write("=" * 60 + "\n\n")
                
                for i, driver in enumerate(drivers, 1):
                    f.write(f"驱动程序 {i}:\n")
                    f.write(f"  程序名称: {driver.get('name', 'N/A')}\n")
                    f.write(f"  版本号: {driver.get('version', 'N/A')}\n")
                    f.write(f"  文件名: {driver.get('filename', 'N/A')}\n")
                    f.write(f"  开发商: {driver.get('manufacturer', 'N/A')}\n")
                    f.write(f"  安装时间: {driver.get('created', 'N/A')}\n")
                    f.write("-" * 40 + "\n")
            
            print(f"✓ 驱动完整性数据已保存到: {self.driver_folder}")
            return True
            
        except Exception as e:
            print(f"保存驱动完整性数据时出错: {e}")
            return False
    
    def save_behavior_data(self, activities):
        """保存行为模式数据到详细文件夹"""
        try:
            # 保存为JSON格式
            json_file = self.behavior_folder / "行为模式数据.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(activities, f, ensure_ascii=False, indent=2)
            
            # 保存为CSV格式
            csv_file = self.behavior_folder / "行为模式数据.csv"
            if activities:
                with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                    writer = csv.DictWriter(f, fieldnames=['time', 'type', 'filename', 'path', 'description'])
                    writer.writeheader()
                    writer.writerows(activities)
            
            # 保存为TXT格式（便于查看）
            txt_file = self.behavior_folder / "行为模式汇总.txt"
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("行为模式分析汇总报告\n")
                f.write("=" * 60 + "\n")
                f.write(f"检测时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"行为记录总数: {len(activities)}\n")
                f.write("=" * 60 + "\n\n")
                
                for i, activity in enumerate(activities, 1):
                    f.write(f"行为记录 {i}:\n")
                    f.write(f"  时间戳: {activity.get('time', 'N/A')}\n")
                    f.write(f"  行为类型: {activity.get('type', 'N/A')}\n")
                    f.write(f"  目标文件: {activity.get('filename', 'N/A')}\n")
                    f.write(f"  文件路径: {activity.get('path', 'N/A')}\n")
                    f.write(f"  行为描述: {activity.get('description', 'N/A')}\n")
                    f.write("-" * 40 + "\n")
            
            print(f"✓ 行为模式数据已保存到: {self.behavior_folder}")
            return True
            
        except Exception as e:
            print(f"保存行为模式数据时出错: {e}")
            return False
    
    def save_all_data(self, data):
        """保存所有检测数据"""
        results = []
        
        # 保存硬件指纹数据
        results.append(self.save_hardware_data(data.get('devices', [])))
        
        # 保存驱动完整性数据
        results.append(self.save_driver_data(data.get('drivers', [])))
        
        # 保存行为模式数据
        results.append(self.save_behavior_data(data.get('activities', [])))
        
        # 创建总览文件
        self._create_overview_file(data)
        
        return all(results)
    
    def _create_overview_file(self, data):
        """创建检测总览文件"""
        try:
            overview_file = self.base_path / f"作弊检测总览_{self.timestamp}.txt"
            with open(overview_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("星星陪玩店 - 选手作弊检测系统数据总览\n")
                f.write("=" * 80 + "\n")
                f.write(f"检测时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"检测工具: 星星陪玩店作弊检测系统 v2.0\n")
                f.write("=" * 80 + "\n\n")
                
                f.write("📊 检测统计:\n")
                f.write(f"  硬件组件数量: {len(data.get('devices', []))}\n")
                f.write(f"  驱动程序数量: {len(data.get('drivers', []))}\n")
                f.write(f"  行为记录数量: {len(data.get('activities', []))}\n\n")
                
                f.write("📁 详细检测数据文件夹:\n")
                f.write(f"  硬件指纹检测: {self.hardware_folder.name}\n")
                f.write(f"  驱动完整性验证: {self.driver_folder.name}\n")
                f.write(f"  行为模式分析: {self.behavior_folder.name}\n\n")
                
                f.write("📄 每个文件夹包含:\n")
                f.write("  - 检测数据.json (JSON格式数据)\n")
                f.write("  - 检测数据.csv (CSV格式数据)\n")
                f.write("  - 检测汇总.txt (文本格式汇总)\n\n")
                
                f.write("💡 使用说明:\n")
                f.write("  - JSON文件适合程序处理\n")
                f.write("  - CSV文件适合Excel分析\n")
                f.write("  - TXT文件适合直接查看\n\n")
                
                f.write("🔍 风险评估:\n")
                device_count = len(data.get('devices', []))
                driver_count = len(data.get('drivers', []))
                activity_count = len(data.get('activities', []))
                
                if device_count > 50:
                    f.write("  - 硬件复杂度: 中等风险\n")
                else:
                    f.write("  - 硬件复杂度: 低风险\n")
                    
                if driver_count > 100:
                    f.write("  - 驱动程序数量: 需要关注\n")
                else:
                    f.write("  - 驱动程序数量: 正常范围\n")
                    
                if activity_count > 1000:
                    f.write("  - 系统活跃度: 高活跃\n")
                else:
                    f.write("  - 系统活跃度: 正常\n")
                
            print(f"✓ 检测总览已保存到: {overview_file}")
            
        except Exception as e:
            print(f"创建总览文件时出错: {e}")
    
    def get_folders_info(self):
        """获取文件夹信息"""
        return {
            'hardware_folder': str(self.hardware_folder),
            'driver_folder': str(self.driver_folder),
            'behavior_folder': str(self.behavior_folder),
            'timestamp': self.timestamp
        }
