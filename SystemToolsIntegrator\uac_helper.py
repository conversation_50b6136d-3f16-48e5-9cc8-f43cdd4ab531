#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UAC权限提升助手
自动检测并请求管理员权限
"""

import sys
import os
import ctypes
import subprocess

def is_admin():
    """检查当前是否具有管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员权限重新启动程序"""
    if is_admin():
        return True
    else:
        try:
            # 获取当前脚本路径
            if getattr(sys, 'frozen', False):
                # 如果是打包的exe
                script = sys.executable
            else:
                # 如果是Python脚本
                script = os.path.abspath(sys.argv[0])
            
            # 使用ShellExecute以管理员权限运行
            params = ' '.join(sys.argv[1:])
            ctypes.windll.shell32.ShellExecuteW(
                None, 
                "runas", 
                script, 
                params, 
                None, 
                1
            )
            return False  # 当前进程应该退出
        except Exception as e:
            print(f"请求管理员权限失败: {e}")
            return False

def check_and_elevate():
    """检查权限并在需要时提升"""
    if not is_admin():
        print("检测到需要管理员权限...")
        print("正在请求权限提升...")
        
        if run_as_admin():
            print("已获得管理员权限")
            return True
        else:
            print("正在以管理员权限重新启动...")
            sys.exit(0)  # 退出当前进程
    else:
        print("已具有管理员权限")
        return True

def get_privilege_status():
    """获取当前权限状态"""
    if is_admin():
        return "管理员权限"
    else:
        return "普通用户权限"

if __name__ == "__main__":
    # 测试权限检查
    print(f"当前权限状态: {get_privilege_status()}")
    
    if not is_admin():
        print("尝试获取管理员权限...")
        check_and_elevate()
    else:
        print("已具有管理员权限，可以继续执行...")
