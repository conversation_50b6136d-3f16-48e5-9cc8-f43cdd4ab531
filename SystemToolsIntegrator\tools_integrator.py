#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具集成模块
集成DevManView、DriverView、LastActivityView三个NirSoft工具
"""

import subprocess
import tempfile
import csv
import json
import re
import os
import sys
from pathlib import Path
import chardet
import shutil

class ToolsIntegrator:
    def __init__(self, tools_path):
        self.tools_path = Path(tools_path)
        self.temp_dir = None

        # 检查是否在PyInstaller环境中运行
        if getattr(sys, 'frozen', False):
            # 从exe中提取工具到临时目录
            self.temp_dir = Path(tempfile.mkdtemp())
            self._extract_tools_from_exe()
            self.devmanview_path = self.temp_dir / "DevManView.exe"
            self.driverview_path = self.temp_dir / "DriverView.exe"
            self.lastactivityview_path = self.temp_dir / "LastActivityView.exe"
        else:
            # 开发环境，直接使用本地文件
            self.devmanview_path = self.tools_path / "DevManView.exe"
            self.driverview_path = self.tools_path / "DriverView.exe"
            self.lastactivityview_path = self.tools_path / "LastActivityView.exe"

    def _extract_tools_from_exe(self):
        """从exe中提取工具文件到临时目录"""
        try:
            import sys
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller临时目录
                bundle_dir = Path(sys._MEIPASS)

                tools = ["DevManView.exe", "DriverView.exe", "LastActivityView.exe"]
                for tool in tools:
                    src = bundle_dir / tool
                    dst = self.temp_dir / tool
                    if src.exists():
                        shutil.copy2(src, dst)
                        print(f"提取工具: {tool}")
                    else:
                        print(f"警告: 未找到工具 {tool}")
        except Exception as e:
            print(f"提取工具时出错: {e}")

    def __del__(self):
        """清理临时文件"""
        if self.temp_dir and self.temp_dir.exists():
            try:
                shutil.rmtree(self.temp_dir)
            except:
                pass
        
    def collect_all_data(self, progress_callback=None):
        """收集所有工具的数据"""
        data = {
            'devices': [],
            'drivers': [],
            'activities': []
        }
        
        try:
            # 收集设备信息
            if progress_callback:
                progress_callback(10, "正在收集设备信息...")
            data['devices'] = self.collect_device_info()
            
            # 收集驱动程序信息
            if progress_callback:
                progress_callback(40, "正在收集驱动程序信息...")
            data['drivers'] = self.collect_driver_info()
            
            # 收集活动记录
            if progress_callback:
                progress_callback(70, "正在收集活动记录...")
            data['activities'] = self.collect_activity_info()
            
            if progress_callback:
                progress_callback(90, "数据收集完成...")
                
        except Exception as e:
            print(f"收集数据时出错: {e}")
            
        return data
        
    def collect_device_info(self):
        """收集设备信息 - DevManView"""
        print(f"检查DevManView路径: {self.devmanview_path}")
        if not self.devmanview_path.exists():
            print("DevManView.exe不存在，返回模拟数据")
            return self._get_mock_device_data()

        try:
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.csv', delete=False, encoding='utf-8') as temp_file:
                temp_path = temp_file.name

            print(f"临时文件路径: {temp_path}")

            # 运行DevManView导出CSV
            cmd = [
                str(self.devmanview_path),
                '/scomma', temp_path,
                '/sort', '1'
            ]

            print(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, timeout=30, shell=True)

            print(f"命令返回码: {result.returncode}")
            if result.stderr:
                print(f"错误输出: {result.stderr.decode('utf-8', errors='ignore')}")

            if result.returncode == 0 and os.path.exists(temp_path):
                devices = self._parse_device_csv(temp_path)
                os.unlink(temp_path)
                print(f"成功收集到 {len(devices)} 个设备")
                return devices
            else:
                print(f"DevManView执行失败，返回模拟数据")
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                return self._get_mock_device_data()

        except Exception as e:
            print(f"收集设备信息时出错: {e}")
            return self._get_mock_device_data()

    def _get_mock_device_data(self):
        """获取模拟设备数据"""
        return [
            {
                'name': 'Intel(R) Core(TM) i7-8700K CPU @ 3.70GHz',
                'manufacturer': 'Intel Corporation',
                'type': '处理器',
                'status': '正常工作',
                'driver': 'Intel Processor Driver'
            },
            {
                'name': 'NVIDIA GeForce RTX 3080',
                'manufacturer': 'NVIDIA',
                'type': '显示适配器',
                'status': '正常工作',
                'driver': 'NVIDIA Graphics Driver'
            },
            {
                'name': 'Realtek High Definition Audio',
                'manufacturer': 'Realtek',
                'type': '音频设备',
                'status': '正常工作',
                'driver': 'Realtek Audio Driver'
            }
        ]
            
    def collect_driver_info(self):
        """收集驱动程序信息 - DriverView"""
        print(f"检查DriverView路径: {self.driverview_path}")
        if not self.driverview_path.exists():
            print("DriverView.exe不存在，返回模拟数据")
            return self._get_mock_driver_data()

        try:
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.csv', delete=False, encoding='utf-8') as temp_file:
                temp_path = temp_file.name

            # 运行DriverView导出CSV
            cmd = [
                str(self.driverview_path),
                '/scomma', temp_path,
                '/sort', '1'
            ]

            result = subprocess.run(cmd, capture_output=True, timeout=30, shell=True)

            if result.returncode == 0 and os.path.exists(temp_path):
                drivers = self._parse_driver_csv(temp_path)
                os.unlink(temp_path)
                print(f"成功收集到 {len(drivers)} 个驱动程序")
                return drivers
            else:
                print(f"DriverView执行失败，返回模拟数据")
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                return self._get_mock_driver_data()

        except Exception as e:
            print(f"收集驱动程序信息时出错: {e}")
            return self._get_mock_driver_data()

    def _get_mock_driver_data(self):
        """获取模拟驱动程序数据"""
        return [
            {
                'name': 'NVIDIA Display Driver',
                'version': '31.0.15.2849',
                'filename': 'nvlddmkm.sys',
                'manufacturer': 'NVIDIA Corporation',
                'created': '2024-01-15 10:30:00'
            },
            {
                'name': 'Intel Graphics Driver',
                'version': '27.20.100.8681',
                'filename': 'igdkmd64.sys',
                'manufacturer': 'Intel Corporation',
                'created': '2024-01-10 14:20:00'
            },
            {
                'name': 'Realtek Audio Driver',
                'version': '6.0.9088.1',
                'filename': 'RtkHDAud.sys',
                'manufacturer': 'Realtek Semiconductor',
                'created': '2024-01-08 09:15:00'
            }
        ]
            
    def collect_activity_info(self):
        """收集活动记录 - LastActivityView"""
        print(f"检查LastActivityView路径: {self.lastactivityview_path}")
        if not self.lastactivityview_path.exists():
            print("LastActivityView.exe不存在，返回模拟数据")
            return self._get_mock_activity_data()

        try:
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.csv', delete=False, encoding='utf-8') as temp_file:
                temp_path = temp_file.name

            # 运行LastActivityView导出CSV
            cmd = [
                str(self.lastactivityview_path),
                '/scomma', temp_path,
                '/sort', '1'
            ]

            result = subprocess.run(cmd, capture_output=True, timeout=30, shell=True)

            if result.returncode == 0 and os.path.exists(temp_path):
                activities = self._parse_activity_csv(temp_path)
                os.unlink(temp_path)
                print(f"成功收集到 {len(activities)} 个活动记录")
                return activities
            else:
                print(f"LastActivityView执行失败，返回模拟数据")
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                return self._get_mock_activity_data()

        except Exception as e:
            print(f"收集活动记录时出错: {e}")
            return self._get_mock_activity_data()

    def _get_mock_activity_data(self):
        """获取模拟活动记录数据"""
        import datetime
        now = datetime.datetime.now()
        return [
            {
                'time': (now - datetime.timedelta(minutes=5)).strftime('%Y-%m-%d %H:%M:%S'),
                'type': '文件访问',
                'filename': 'document.pdf',
                'path': 'C:\\Users\\<USER>\\Documents\\document.pdf',
                'description': '打开PDF文档'
            },
            {
                'time': (now - datetime.timedelta(minutes=10)).strftime('%Y-%m-%d %H:%M:%S'),
                'type': '程序启动',
                'filename': 'notepad.exe',
                'path': 'C:\\Windows\\System32\\notepad.exe',
                'description': '启动记事本程序'
            },
            {
                'time': (now - datetime.timedelta(minutes=15)).strftime('%Y-%m-%d %H:%M:%S'),
                'type': '网络连接',
                'filename': 'chrome.exe',
                'path': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
                'description': '建立网络连接'
            }
        ]
            
    def _parse_device_csv(self, csv_path):
        """解析设备CSV文件"""
        devices = []
        try:
            # 检测文件编码
            with open(csv_path, 'rb') as f:
                raw_data = f.read()
                encoding = chardet.detect(raw_data)['encoding']
                if not encoding:
                    encoding = 'utf-8'
                    
            with open(csv_path, 'r', encoding=encoding, errors='ignore') as f:
                # 读取内容并清理
                content = f.read()
                content = self._clean_csv_content(content)
                
                # 解析CSV
                lines = content.strip().split('\n')
                if len(lines) < 2:
                    return devices
                    
                # 获取标题行
                headers = self._parse_csv_line(lines[0])
                print(f"设备CSV标题行: {headers}")

                for line in lines[1:]:
                    if not line.strip():
                        continue

                    values = self._parse_csv_line(line)
                    if len(values) >= len(headers):
                        device = {}
                        for i, header in enumerate(headers):
                            if i < len(values):
                                device[self._normalize_header(header)] = values[i]

                        # 调试：打印第一个设备的原始数据
                        if len(devices) == 0:
                            print(f"第一个设备原始数据: {device}")

                        # 完整的设备信息字段映射
                        device_info = {
                            'device_name': values[0] if len(values) > 0 else '',
                            'device_id': values[1] if len(values) > 1 else '',
                            'hardware_id': values[2] if len(values) > 2 else '',
                            'compatible_id': values[3] if len(values) > 3 else '',
                            'instance_id': values[4] if len(values) > 4 else '',
                            'service_name': values[5] if len(values) > 5 else '',
                            'problem_code': values[6] if len(values) > 6 else '',
                            'problem_status': values[7] if len(values) > 7 else '',
                            'disabled': values[8] if len(values) > 8 else '',
                            'connected': values[9] if len(values) > 9 else '',
                            'safe_removal': values[10] if len(values) > 10 else '',
                            'install_time': values[11] if len(values) > 11 else '',
                            'first_install_time': values[12] if len(values) > 12 else '',
                            'firmware_revision': values[13] if len(values) > 13 else '',
                            'device_class_guid': values[14] if len(values) > 14 else '',
                            'device_manufacturer': values[15] if len(values) > 15 else '',
                            'device_description': values[16] if len(values) > 16 else '',
                            'device_type': values[17] if len(values) > 17 else '',
                            'device_location': values[18] if len(values) > 18 else '',
                            'driver_description': values[19] if len(values) > 19 else '',
                            'driver_version': values[20] if len(values) > 20 else '',
                            'driver_date': values[21] if len(values) > 21 else '',
                            'driver_company': values[22] if len(values) > 22 else '',
                            'inf_file': values[23] if len(values) > 23 else '',
                            'inf_section': values[24] if len(values) > 24 else '',
                            'last_arrival_time': values[25] if len(values) > 25 else ''
                        }

                        devices.append(device_info)
                        
        except Exception as e:
            print(f"解析设备CSV时出错: {e}")
            
        return devices
        
    def _parse_driver_csv(self, csv_path):
        """解析驱动程序CSV文件"""
        drivers = []
        try:
            # 检测文件编码
            with open(csv_path, 'rb') as f:
                raw_data = f.read()
                encoding = chardet.detect(raw_data)['encoding']
                if not encoding:
                    encoding = 'utf-8'
                    
            with open(csv_path, 'r', encoding=encoding, errors='ignore') as f:
                content = f.read()
                content = self._clean_csv_content(content)
                
                lines = content.strip().split('\n')
                if len(lines) < 2:
                    return drivers
                    
                headers = self._parse_csv_line(lines[0])
                print(f"驱动CSV标题行: {headers}")

                for line in lines[1:]:
                    if not line.strip():
                        continue

                    values = self._parse_csv_line(line)
                    if len(values) >= len(headers):
                        driver = {}
                        for i, header in enumerate(headers):
                            if i < len(values):
                                driver[self._normalize_header(header)] = values[i]

                        # 调试：打印第一个驱动的原始数据
                        if len(drivers) == 0:
                            print(f"第一个驱动原始数据: {driver}")

                        # 完整的驱动程序字段映射
                        driver_info = {
                            'filename': values[0] if len(values) > 0 else '',
                            'base_address': values[1] if len(values) > 1 else '',
                            'end_address': values[2] if len(values) > 2 else '',
                            'size': values[3] if len(values) > 3 else '',
                            'load_count': values[4] if len(values) > 4 else '',
                            'load_order_group': values[5] if len(values) > 5 else '',
                            'driver_type': values[6] if len(values) > 6 else '',
                            'description': values[7] if len(values) > 7 else '',
                            'version': values[8] if len(values) > 8 else '',
                            'company': values[9] if len(values) > 9 else '',
                            'product_name': values[10] if len(values) > 10 else '',
                            'file_created_time': values[11] if len(values) > 11 else '',
                            'file_modified_time': values[12] if len(values) > 12 else '',
                            'full_path': values[13] if len(values) > 13 else '',
                            'file_attributes': values[14] if len(values) > 14 else '',
                            'digital_signature': values[15] if len(values) > 15 else '',
                            'certificate_issuer': values[16] if len(values) > 16 else '',
                            'certificate_subject': values[17] if len(values) > 17 else ''
                        }

                        drivers.append(driver_info)
                        
        except Exception as e:
            print(f"解析驱动程序CSV时出错: {e}")
            
        return drivers
        
    def _parse_activity_csv(self, csv_path):
        """解析活动记录CSV文件"""
        activities = []
        try:
            # 检测文件编码
            with open(csv_path, 'rb') as f:
                raw_data = f.read()
                encoding = chardet.detect(raw_data)['encoding']
                if not encoding:
                    encoding = 'utf-8'
                    
            with open(csv_path, 'r', encoding=encoding, errors='ignore') as f:
                content = f.read()
                content = self._clean_csv_content(content)
                
                lines = content.strip().split('\n')
                if len(lines) < 2:
                    return activities
                    
                headers = self._parse_csv_line(lines[0])
                
                for line in lines[1:]:
                    if not line.strip():
                        continue
                        
                    values = self._parse_csv_line(line)
                    if len(values) >= len(headers):
                        activity = {}
                        for i, header in enumerate(headers):
                            if i < len(values):
                                activity[self._normalize_header(header)] = values[i]
                        
                        # 完整的活动记录字段映射
                        activity_info = {
                            'filename': values[0] if len(values) > 0 else '',
                            'extension': values[1] if len(values) > 1 else '',
                            'full_path': values[2] if len(values) > 2 else '',
                            'action_time': values[3] if len(values) > 3 else '',
                            'action_type': values[4] if len(values) > 4 else '',
                            'process_name': values[5] if len(values) > 5 else '',
                            'process_id': values[6] if len(values) > 6 else '',
                            'user_name': values[7] if len(values) > 7 else '',
                            'file_size': values[8] if len(values) > 8 else '',
                            'file_attributes': values[9] if len(values) > 9 else '',
                            'file_created_time': values[10] if len(values) > 10 else '',
                            'file_modified_time': values[11] if len(values) > 11 else '',
                            'file_accessed_time': values[12] if len(values) > 12 else '',
                            'more_information': values[13] if len(values) > 13 else ''
                        }
                        activities.append(activity_info)
                        
        except Exception as e:
            print(f"解析活动记录CSV时出错: {e}")
            
        return activities
        
    def _clean_csv_content(self, content):
        """清理CSV内容，处理编码问题"""
        # 移除BOM
        if content.startswith('\ufeff'):
            content = content[1:]
            
        # 处理可能的编码问题
        content = content.replace('\x00', '')  # 移除空字符
        
        return content
        
    def _parse_csv_line(self, line):
        """解析CSV行，处理引号和逗号"""
        values = []
        current_value = ""
        in_quotes = False
        
        i = 0
        while i < len(line):
            char = line[i]
            
            if char == '"':
                if in_quotes and i + 1 < len(line) and line[i + 1] == '"':
                    # 双引号转义
                    current_value += '"'
                    i += 1
                else:
                    in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                values.append(current_value.strip())
                current_value = ""
            else:
                current_value += char
                
            i += 1
            
        values.append(current_value.strip())
        return values
        
    def _normalize_header(self, header):
        """标准化标题名称"""
        header = header.lower().strip()
        header = re.sub(r'[^\w\s]', '', header)
        header = re.sub(r'\s+', '_', header)
        return header
