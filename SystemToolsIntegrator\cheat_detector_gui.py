#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
星星陪玩店 - 选手作弊检测工具
伪装界面，隐去真实工具信息
"""

import sys
import os
import subprocess
import datetime
import json
import webbrowser
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import tempfile
import shutil

# 导入自定义模块
try:
    from uac_helper import is_admin, get_privilege_status
    from tools_integrator import ToolsIntegrator
    from cheat_report_generator import CheatReportGenerator
    from cheat_file_manager import CheatFileManager
    from icon_fix import fix_window_icon, create_icon_if_missing
except ImportError as e:
    print(f"导入模块失败: {e}")

class CheatDetectorGUI:
    def __init__(self):
        print("🔧 开始初始化CheatDetectorGUI...")
        try:
            print("🔧 创建主窗口...")
            self.root = tk.Tk()
            print("✓ 主窗口创建成功")

            print("🔧 检查管理员状态...")
            self.admin_status = is_admin()
            print(f"✓ 管理员状态: {self.admin_status}")

            print("🔧 设置窗口...")
            self.setup_window()
            print("✓ 窗口设置完成")

            print("🔧 设置变量...")
            self.setup_variables()
            print("✓ 变量设置完成")

            print("🔧 直接显示主界面...")
            self.setup_main_ui()
            print("✓ 主界面显示完成")

        except Exception as e:
            print(f"❌ GUI初始化失败: {e}")
            import traceback
            traceback.print_exc()
            raise
        
    def setup_window(self):
        """设置主窗口"""
        try:
            print("🔧 设置窗口标题...")
            self.root.title("星星陪玩店 - 选手作弊检测系统")
            print("✓ 窗口标题设置完成")

            print("🔧 设置窗口大小...")
            self.root.geometry("1000x700")
            print("✓ 窗口大小设置完成")

            print("🔧 设置窗口可调整大小...")
            self.root.resizable(True, True)
            print("✓ 窗口可调整大小设置完成")
        except Exception as e:
            print(f"❌ 设置窗口基本属性失败: {e}")
            raise
        
        try:
            # 设置图标
            print("🔧 设置窗口图标...")
            icon_path = Path(__file__).parent / "1.ico"
            if icon_path.exists():
                print(f"🔧 找到图标文件: {icon_path}")
                try:
                    self.root.iconbitmap(str(icon_path))
                    self.root.wm_iconbitmap(str(icon_path))
                    print("✓ 窗口图标设置完成")
                except Exception as e:
                    print(f"❌ 设置图标失败: {e}")
            else:
                print("⚠️ 图标文件不存在")
        except Exception as e:
            print(f"❌ 图标设置过程失败: {e}")
        else:
            try:
                created_icon = create_icon_if_missing()
                if created_icon:
                    self.root.iconbitmap(created_icon)
            except Exception as e:
                print(f"创建图标失败: {e}")
        
        try:
            # 现代化配色方案
            print("🔧 设置颜色主题...")
            self.colors = {
                'primary': '#e74c3c',      # 主色调 - 红色（警告色）
                'secondary': '#c0392b',    # 次要色 - 深红色
                'accent': '#f39c12',       # 强调色 - 橙色
                'success': '#27ae60',      # 成功色 - 绿色
                'warning': '#f1c40f',      # 警告色 - 黄色
                'danger': '#e74c3c',       # 危险色 - 红色
                'light': '#ecf0f1',        # 浅色
                'dark': '#2c3e50',         # 深色
                'white': '#ffffff',        # 白色
                'gray': '#7f8c8d',         # 灰色
                'text': '#2c3e50'          # 文本色 - 深色
            }
            print("✓ 颜色主题设置完成")

            print("🔧 设置根窗口背景色...")
            self.root.configure(bg=self.colors['light'])
            print("✓ 根窗口背景色设置完成")
        except Exception as e:
            print(f"❌ 颜色主题设置失败: {e}")
            raise
        
    def setup_variables(self):
        """设置变量"""
        try:
            print("🔧 设置路径变量...")
            # 获取程序运行目录
            if getattr(sys, 'frozen', False):
                # 如果是打包的exe文件
                self.tools_path = Path(sys.executable).parent
            else:
                # 如果是Python脚本
                self.tools_path = Path(__file__).parent

            # 获取桌面路径并创建报告文件夹
            import os
            try:
                # 尝试多种方式获取桌面路径
                desktop_path = None

                # 方法1：使用环境变量
                if os.environ.get('USERPROFILE'):
                    desktop_path = Path(os.environ['USERPROFILE']) / "Desktop"

                # 方法2：使用expanduser
                if not desktop_path or not desktop_path.exists():
                    desktop_path = Path(os.path.expanduser("~")) / "Desktop"

                # 方法3：使用当前用户目录
                if not desktop_path or not desktop_path.exists():
                    import getpass
                    username = getpass.getuser()
                    desktop_path = Path(f"C:/Users/<USER>/Desktop")

                # 方法4：如果都失败，使用程序目录
                if not desktop_path or not desktop_path.exists():
                    desktop_path = self.tools_path
                    print("⚠️ 无法找到桌面路径，使用程序目录")

                # 创建报告文件夹（使用英文名避免路径问题）
                self.output_path = desktop_path / "StarCafe_AntiCheat_Reports"
                self.output_path.mkdir(exist_ok=True)
                print(f"✓ 报告保存路径: {self.output_path}")

            except Exception as e:
                print(f"⚠️ 桌面路径设置失败: {e}")
                # 备用方案：使用程序目录
                self.output_path = self.tools_path / "Reports"
                self.output_path.mkdir(exist_ok=True)
                print(f"✓ 使用备用路径: {self.output_path}")
            print("✓ 路径变量设置完成")

            print("🔧 设置状态变量...")
            # 状态变量
            self.is_running = False
            self.progress_var = tk.DoubleVar()
            self.status_var = tk.StringVar(value="系统就绪")
            print("✓ 状态变量设置完成")
        except Exception as e:
            print(f"❌ 变量设置失败: {e}")
            raise

    def show_gentle_dialog(self):
        """显示温柔风格的弹窗"""
        try:
            print("🌸 创建温柔弹窗...")

            # 创建弹窗
            dialog = tk.Toplevel(self.root)
            dialog.title("💖 星星陪玩店 - 陪玩作弊检测系统")
            dialog.geometry("550x450")
            dialog.resizable(False, False)

            # 温柔的配色方案
            gentle_colors = {
                'bg': '#f8f4f0',           # 温暖的米白色
                'primary': '#d4a574',      # 温柔的金棕色
                'secondary': '#e8d5c4',    # 柔和的浅棕色
                'text': '#5d4e37',         # 温暖的深棕色
                'accent': '#c9a96e',       # 优雅的金色
                'white': '#ffffff'
            }

            dialog.configure(bg=gentle_colors['bg'])

            # 居中显示
            dialog.transient(self.root)
            self.root.withdraw()

            # 主容器
            main_frame = tk.Frame(dialog, bg=gentle_colors['bg'])
            main_frame.pack(fill=tk.BOTH, expand=True, padx=25, pady=25)

            # 温柔的标题
            title_label = tk.Label(
                main_frame,
                text="💖 星星陪玩店",
                font=("Microsoft YaHei", 20, "bold"),
                fg=gentle_colors['primary'],
                bg=gentle_colors['bg']
            )
            title_label.pack(pady=(0, 8))

            subtitle_label = tk.Label(
                main_frame,
                text="陪玩作弊检测系统",
                font=("Microsoft YaHei", 14),
                fg=gentle_colors['text'],
                bg=gentle_colors['bg']
            )
            subtitle_label.pack(pady=(0, 20))

            # 温柔的分隔线
            separator = tk.Frame(main_frame, height=2, bg=gentle_colors['secondary'])
            separator.pack(fill=tk.X, pady=(0, 20))

            # 温柔的功能介绍
            intro_text = """🌟 亲爱的管理员，欢迎使用我们的检测系统

🎯 温馨提示：
   • 硬件指纹检测 - 轻柔检查硬件配置
   • 驱动完整性验证 - 温和监控驱动程序
   • 行为模式分析 - 细心分析系统活动

📊 贴心服务：
   • 生成清晰易读的CSV报告
   • 支持Excel表格便于查看
   • 详细记录供您参考

🌸 隐私承诺：
   • 仅收集必要信息，保护隐私
   • 本地处理，绝不上传云端"""

            info_label = tk.Label(
                main_frame,
                text=intro_text,
                font=("Microsoft YaHei", 10),
                fg=gentle_colors['text'],
                bg=gentle_colors['bg'],
                justify=tk.LEFT,
                anchor='w'
            )
            info_label.pack(pady=(0, 25), fill=tk.X)

            # 温柔的按钮容器
            button_frame = tk.Frame(main_frame, bg=gentle_colors['bg'])
            button_frame.pack(fill=tk.X)

            # 温柔的确定按钮
            def close_dialog():
                try:
                    dialog.destroy()
                    self.root.deiconify()
                    self.setup_main_ui()
                except Exception as e:
                    print(f"关闭弹窗时出错: {e}")

            ok_button = tk.Button(
                button_frame,
                text="💝 好的，开始温柔检测",
                command=close_dialog,
                font=("Microsoft YaHei", 12, "bold"),
                fg=gentle_colors['white'],
                bg=gentle_colors['primary'],
                activebackground=gentle_colors['accent'],
                activeforeground=gentle_colors['white'],
                relief=tk.FLAT,
                bd=0,
                cursor="hand2",
                padx=25,
                pady=12
            )
            ok_button.pack(side=tk.RIGHT)

            # 添加温柔的圆角效果（通过padding模拟）
            ok_button.configure(relief=tk.RAISED, bd=1)

            print("✓ 温柔弹窗创建完成")

        except Exception as e:
            print(f"❌ 创建温柔弹窗失败: {e}")
            # 如果失败，直接显示主界面
            try:
                self.root.deiconify()
                self.setup_main_ui()
            except Exception as e2:
                print(f"❌ 显示主界面也失败: {e2}")

    def show_intro_animation_simple(self):
        """显示简化的介绍窗口 - 无复杂动画"""
        try:
            print("🎬 创建简化介绍窗口...")

            # 创建介绍窗口
            intro_window = tk.Toplevel(self.root)
            intro_window.title("关于选手作弊检测系统")
            intro_window.geometry("600x400")
            intro_window.resizable(False, False)
            intro_window.configure(bg=self.colors['dark'])

            # 居中显示
            intro_window.transient(self.root)

            # 隐藏主窗口
            self.root.withdraw()

            # 创建内容
            content_frame = tk.Frame(intro_window, bg=self.colors['dark'])
            content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

            # 标题
            title_label = tk.Label(
                content_frame,
                text="🛡️ 星星陪玩店 - 选手作弊检测系统",
                font=("Microsoft YaHei", 18, "bold"),
                fg=self.colors['primary'],
                bg=self.colors['dark']
            )
            title_label.pack(pady=(0, 20))

            # 简化的功能介绍
            intro_text = """
🎯 检测功能：
• 硬件指纹检测 - 检测硬件配置异常
• 驱动完整性验证 - 监控可疑驱动程序
• 行为模式分析 - 分析最近系统活动

📊 报告格式：
• CSV格式便于数据分析
• Excel表格支持筛选排序
• 详细日志文件备查

🔒 隐私保护：
• 仅收集必要的系统信息
• 本地处理，不上传云端
            """

            info_label = tk.Label(
                content_frame,
                text=intro_text,
                font=("Microsoft YaHei", 11),
                fg=self.colors['light'],
                bg=self.colors['dark'],
                justify=tk.LEFT
            )
            info_label.pack(pady=(0, 20))

            # 确定按钮
            def close_intro():
                try:
                    intro_window.destroy()
                    self.root.deiconify()
                    self.setup_main_ui()
                except Exception as e:
                    print(f"关闭介绍窗口时出错: {e}")

            ok_button = tk.Button(
                content_frame,
                text="确定，开始检测",
                command=close_intro,
                font=("Microsoft YaHei", 12, "bold"),
                fg=self.colors['white'],
                bg=self.colors['primary'],
                relief=tk.FLAT,
                bd=0,
                cursor="hand2",
                padx=30,
                pady=10
            )
            ok_button.pack()

            print("✓ 简化介绍窗口创建完成")

        except Exception as e:
            print(f"❌ 创建简化介绍窗口失败: {e}")
            # 如果失败，直接显示主界面
            try:
                self.root.deiconify()
                self.setup_main_ui()
            except Exception as e2:
                print(f"❌ 显示主界面也失败: {e2}")

    def show_intro_animation(self):
        """显示带动画效果的介绍窗口"""
        try:
            print("🎬 开始创建介绍窗口...")

            # 创建介绍窗口
            print("🎬 创建Toplevel窗口...")
            intro_window = tk.Toplevel(self.root)
            print("✓ Toplevel窗口创建成功")

            print("🎬 设置窗口属性...")
            intro_window.title("关于选手作弊检测系统")
            intro_window.geometry("600x500")
            intro_window.resizable(False, False)
            intro_window.configure(bg=self.colors['dark'])
            print("✓ 窗口属性设置完成")

            # 居中显示
            print("🎬 设置窗口关系...")
            intro_window.transient(self.root)
            # 暂时不使用grab_set，可能导致阻塞
            # intro_window.grab_set()
            print("✓ 窗口关系设置完成")

            # 隐藏主窗口
            print("🎬 隐藏主窗口...")
            self.root.withdraw()
            print("✓ 主窗口已隐藏")

            # 创建内容框架
            print("🎬 创建内容框架...")
            content_frame = tk.Frame(intro_window, bg=self.colors['dark'])
            content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
            print("✓ 内容框架创建完成")

            # 标题
            print("🎬 创建标题...")
            title_label = tk.Label(
                content_frame,
                text="🛡️ 星星陪玩店",
                font=("Microsoft YaHei", 24, "bold"),
                fg=self.colors['primary'],
                bg=self.colors['dark']
            )
            title_label.pack(pady=(0, 10))
            print("✓ 标题创建完成")

            subtitle_label = tk.Label(
                content_frame,
                text="选手作弊检测系统",
                font=("Microsoft YaHei", 18),
                fg=self.colors['white'],
                bg=self.colors['dark']
            )
            subtitle_label.pack(pady=(0, 30))
            print("✓ 副标题创建完成")

            # 功能介绍
            print("🎬 创建功能介绍...")
            intro_text = """
🎯 系统功能：
• 检测选手硬件配置异常
• 监控可疑驱动程序安装
• 分析最近系统活动记录
• 生成详细的检测报告

⚠️ 检测原理：
• 硬件指纹识别技术
• 驱动程序完整性验证
• 系统行为模式分析
• 多维度数据交叉验证

📊 报告格式：
• CSV格式便于数据分析
• Excel表格支持筛选排序
• 详细日志文件备查
• 可疑项目重点标记

🔒 隐私保护：
• 仅收集必要的系统信息
• 不涉及个人隐私数据
• 本地处理，不上传云端
• 符合数据保护规范
            """

            info_label = tk.Label(
                content_frame,
                text=intro_text,
                font=("Microsoft YaHei", 11),
                fg=self.colors['light'],
                bg=self.colors['dark'],
                justify=tk.LEFT
            )
            info_label.pack(pady=(0, 30))
            print("✓ 功能介绍创建完成")

            # 版权信息
            print("🎬 创建版权信息...")
            copyright_label = tk.Label(
                content_frame,
                text="© 2025 星星陪玩店技术部 | 内部使用工具",
                font=("Microsoft YaHei", 9),
                fg=self.colors['gray'],
                bg=self.colors['dark']
            )
            copyright_label.pack(pady=(0, 20))
            print("✓ 版权信息创建完成")

            # 确定按钮
            print("🎬 创建确定按钮...")
            def close_intro():
                try:
                    print("🔚 用户点击确定按钮")
                    intro_window.destroy()
                    self.root.deiconify()  # 显示主窗口
                    self.setup_main_ui()
                except Exception as e:
                    print(f"关闭介绍窗口时出错: {e}")
                    # 确保主窗口显示
                    try:
                        self.root.deiconify()
                        self.setup_main_ui()
                    except Exception as e2:
                        print(f"设置主界面时出错: {e2}")
                        # 最后的备用方案
                        import tkinter.messagebox as msgbox
                        msgbox.showerror("错误", f"界面初始化失败: {e2}")

            ok_button = tk.Button(
                content_frame,
                text="确定，开始检测",
                command=close_intro,
                font=("Microsoft YaHei", 12, "bold"),
                fg=self.colors['white'],
                bg=self.colors['primary'],
                activebackground=self.colors['secondary'],
                activeforeground=self.colors['white'],
                relief=tk.FLAT,
                bd=0,
                cursor="hand2",
                padx=30,
                pady=10
            )
            ok_button.pack()
            print("✓ 确定按钮创建完成")
        
            # 使用改进的淡入动画效果
            print("🎬 开始淡入动画...")
            intro_window.attributes('-alpha', 0.0)
            self.fade_in_safe(intro_window)
            print("✓ 淡入动画启动完成")

        except Exception as e:
            print(f"❌ 创建介绍窗口失败: {e}")
            import traceback
            traceback.print_exc()

            # 如果介绍窗口创建失败，直接显示主界面
            try:
                print("🔧 尝试直接显示主界面...")
                self.root.deiconify()
                self.setup_main_ui()
                print("✓ 直接显示主界面成功")
            except Exception as e2:
                print(f"❌ 直接显示主界面也失败: {e2}")
                import tkinter.messagebox as msgbox
                msgbox.showerror("严重错误", f"界面初始化完全失败:\n{e}\n{e2}")
        
    def fade_in_safe(self, window):
        """安全的淡入动画效果 - 避免递归调用问题"""
        try:
            print("🎬 开始执行淡入动画...")
            # 直接设置为可见，不使用复杂动画
            window.attributes('-alpha', 1.0)
            print("✓ 淡入动画完成（直接显示）")

        except Exception as e:
            print(f"❌ 淡入动画失败: {e}")
            # 如果动画失败，确保窗口可见
            try:
                window.attributes('-alpha', 1.0)
            except:
                pass
        
    def setup_main_ui(self):
        """设置主界面"""
        try:
            print("开始设置主界面...")

            # 创建主容器
            main_container = tk.Frame(self.root, bg=self.colors['light'])
            main_container.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
            print("✓ 主容器创建成功")

            # 顶部标题栏
            self.create_header(main_container)
            print("✓ 标题栏创建成功")

            # 中间内容区域
            content_frame = tk.Frame(main_container, bg=self.colors['light'])
            content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)
            print("✓ 内容区域创建成功")

            # 检测模块卡片
            self.create_detection_cards(content_frame)
            print("✓ 检测模块卡片创建成功")

            # 操作按钮区域
            self.create_action_buttons(content_frame)
            print("✓ 操作按钮区域创建成功")

            # AI分析区域
            self.create_ai_analysis_section(content_frame)
            print("✓ AI分析区域创建成功")

            # 进度条区域
            self.create_progress_section(content_frame)
            print("✓ 进度条区域创建成功")

            # 底部状态栏
            self.create_status_bar(main_container)
            print("✓ 状态栏创建成功")

            print("✓ 主界面设置完成")

        except Exception as e:
            print(f"❌ 设置主界面时出错: {e}")
            import traceback
            traceback.print_exc()

            # 显示错误信息
            try:
                import tkinter.messagebox as msgbox
                msgbox.showerror("界面错误", f"主界面设置失败:\n{e}")
            except:
                pass
        
    def create_header(self, parent):
        """创建顶部标题栏"""
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=120)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # 严肃的标题
        title_label = tk.Label(
            header_frame,
            text="⚠️ 星星陪玩店",
            font=("Microsoft YaHei", 28, "bold"),
            fg=self.colors['white'],
            bg=self.colors['primary']
        )
        title_label.pack(pady=(15, 3))

        # 严肃的副标题
        subtitle_label = tk.Label(
            header_frame,
            text="反作弊检测系统 | 严厉打击作弊行为 ",
            font=("Microsoft YaHei", 12),
            fg=self.colors['light'],
            bg=self.colors['primary']
        )
        subtitle_label.pack()

        # 严肃警告语
        warning_label = tk.Label(
            header_frame,
            text="⚡ 系统正在监控，发现作弊行为将严肃处理",
            font=("Microsoft YaHei", 10),
            fg=self.colors['warning'],
            bg=self.colors['primary']
        )
        warning_label.pack(pady=(2, 0))
        
        # 权限状态
        privilege_text = f"当前权限: {get_privilege_status()}"
        privilege_color = self.colors['success'] if self.admin_status else self.colors['warning']
        privilege_label = tk.Label(
            header_frame,
            text=privilege_text,
            font=("Microsoft YaHei", 10),
            fg=privilege_color,
            bg=self.colors['primary']
        )
        privilege_label.pack(pady=(5, 15))
        
    def create_detection_cards(self, parent):
        """创建检测模块卡片"""
        cards_frame = tk.Frame(parent, bg=self.colors['light'])
        cards_frame.pack(fill=tk.X, pady=(0, 30))
        
        # 检测模块信息
        modules_info = [
            {
                'name': '硬件指纹追踪',
                'desc': '严密监控硬件异常变化',
                'icon': '[HW]',
                'color': self.colors['primary']
            },
            {
                'name': '驱动完整性扫描',
                'desc': '深度检测可疑驱动程序',
                'icon': '[DRV]',
                'color': self.colors['secondary']
            },
            {
                'name': '行为模式监控',
                'desc': '全面分析异常系统活动',
                'icon': '[HW]',
                'color': self.colors['accent']
            }
        ]
        
        for i, module in enumerate(modules_info):
            card = self.create_detection_card(cards_frame, module)
            card.grid(row=0, column=i, padx=15, sticky="ew")
            
        # 配置列权重
        for i in range(3):
            cards_frame.grid_columnconfigure(i, weight=1)

    def create_detection_card(self, parent, module_info):
        """创建单个检测模块卡片"""
        # 卡片容器
        card_frame = tk.Frame(
            parent,
            bg=self.colors['white'],
            relief=tk.FLAT,
            bd=0
        )

        # 图标和标题
        icon_label = tk.Label(
            card_frame,
            text=module_info['icon'],
            font=("Segoe UI Emoji", 32),
            bg=self.colors['white'],
            fg=module_info['color']
        )
        icon_label.pack(pady=(20, 10))

        # 模块名称
        name_label = tk.Label(
            card_frame,
            text=module_info['name'],
            font=("Microsoft YaHei", 14, "bold"),
            fg=self.colors['dark'],
            bg=self.colors['white']
        )
        name_label.pack(pady=(0, 5))

        # 模块描述
        desc_label = tk.Label(
            card_frame,
            text=module_info['desc'],
            font=("Microsoft YaHei", 10),
            fg=self.colors['gray'],
            bg=self.colors['white']
        )
        desc_label.pack(pady=(0, 10))

        # 状态指示器
        status_label = tk.Label(
            card_frame,
            text="✓ 模块就绪",
            font=("Microsoft YaHei", 10, "bold"),
            fg=self.colors['success'],
            bg=self.colors['white']
        )
        status_label.pack(pady=(0, 20))

        return card_frame

    def create_action_buttons(self, parent):
        """创建操作按钮区域"""
        buttons_frame = tk.Frame(parent, bg=self.colors['light'])
        buttons_frame.pack(fill=tk.X, pady=(0, 30))

        # 按钮容器
        btn_container = tk.Frame(buttons_frame, bg=self.colors['light'])
        btn_container.pack(expand=True)

        # 主要操作按钮
        self.detect_btn = self.create_modern_button(
            btn_container,
            text=" 开始反作弊检测",
            command=self.start_detection,
            bg_color=self.colors['primary'],
            width=200,
            height=50
        )
        self.detect_btn.pack(side=tk.LEFT, padx=10)

        # 次要操作按钮
        self.report_btn = self.create_modern_button(
            btn_container,
            text="📁 查看检测报告",
            command=self.open_reports_folder,
            bg_color=self.colors['secondary'],
            width=180,
            height=50
        )
        self.report_btn.pack(side=tk.LEFT, padx=10)

    def create_modern_button(self, parent, text, command, bg_color, width=120, height=40):
        """创建现代化按钮"""
        btn = tk.Button(
            parent,
            text=text,
            command=command,
            font=("Microsoft YaHei", 11, "bold"),
            fg=self.colors['white'],
            bg=bg_color,
            activebackground=self._darken_color(bg_color),
            activeforeground=self.colors['white'],
            relief=tk.FLAT,
            bd=0,
            cursor="hand2",
            width=width//8,
            height=height//20
        )

        # 绑定悬停效果
        def on_enter(e):
            btn.configure(bg=self._lighten_color(bg_color))

        def on_leave(e):
            btn.configure(bg=bg_color)

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)

        return btn

    def create_ai_analysis_section(self, parent):
        """创建AI分析区域"""
        # AI分析框架
        ai_frame = tk.Frame(parent, bg=self.colors['light'])
        ai_frame.pack(fill=tk.X, pady=(0, 30))

        # AI分析标题
        title_frame = tk.Frame(ai_frame, bg=self.colors['accent'])
        title_frame.pack(fill=tk.X, pady=(0, 15))

        title_label = tk.Label(
            title_frame,
            text="🤖 AI智能分析系统",
            font=("Microsoft YaHei", 14, "bold"),
            fg="white",
            bg=self.colors['accent']
        )
        title_label.pack(pady=10)

        # AI分析说明
        desc_label = tk.Label(
            ai_frame,
            text="基于Claude AI模型深度分析检测报告，智能识别作弊行为模式",
            font=("Microsoft YaHei", 10),
            fg=self.colors['text'],
            bg=self.colors['light']
        )
        desc_label.pack(pady=(0, 15))

        # API配置区域
        config_frame = tk.Frame(ai_frame, bg=self.colors['light'])
        config_frame.pack(fill=tk.X, pady=(0, 15))

        # API配置说明（隐匿API Key）
        api_label = tk.Label(
            config_frame,
            text="AI分析引擎：已配置星星陪玩店专用AI接口",
            font=("Microsoft YaHei", 10),
            fg=self.colors['success'],
            bg=self.colors['light']
        )
        api_label.pack(anchor=tk.W, pady=(0, 10))

        # 隐匿设置API Key
        self.api_key_var = tk.StringVar()
        self.api_key_var.set("sk-rYoGU5tH7gfed2jmsTfJNLhEnlM5FXYsihrorEMYUjwQOEoK")

        # AI分析按钮
        ai_btn = self.create_modern_button(
            ai_frame,
            "🧠 启动AI智能分析",
            self.start_ai_analysis,
            self.colors['accent']
        )
        ai_btn.pack(pady=10)

        # AI分析状态
        self.ai_status_var = tk.StringVar(value="等待启动AI分析...")
        ai_status_label = tk.Label(
            ai_frame,
            textvariable=self.ai_status_var,
            font=("Microsoft YaHei", 10),
            fg=self.colors['secondary'],
            bg=self.colors['light']
        )
        ai_status_label.pack(pady=(10, 0))

    def start_ai_analysis(self):
        """启动AI分析"""
        print("🤖 AI分析按钮被点击")

        if not self.api_key_var.get().strip():
            print("❌ API Key为空")
            messagebox.showerror("错误", "请先输入Claude API Key")
            return

        if not hasattr(self, 'output_path') or not self.output_path:
            print("❌ 未找到输出路径")
            messagebox.showerror("错误", "请先运行检测生成报告文件")
            return

        print("✓ 开始启动AI分析线程")
        # 在新线程中运行AI分析
        threading.Thread(target=self._run_ai_analysis, daemon=True).start()

    def _run_ai_analysis(self):
        """在后台运行AI分析"""
        try:
            print("🤖 开始AI分析后台线程")
            self.ai_status_var.set("🔍 正在读取检测报告...")

            # 查找最新的报告文件
            print("🔍 查找最新报告文件...")
            report_files = self._find_latest_reports()
            print(f"📁 找到报告文件: {report_files}")

            if not report_files:
                print("❌ 未找到检测报告文件")
                self.ai_status_var.set("❌ 未找到检测报告文件")
                return

            self.ai_status_var.set("🤖 正在连接Claude AI...")
            print("🧠 开始调用AI分析...")

            # 调用Claude AI分析
            analysis_result = self._call_claude_ai(report_files)
            print(f"📊 AI分析结果: {analysis_result[:100] if analysis_result else 'None'}...")

            if analysis_result:
                self.ai_status_var.set("📊 正在生成可视化报告...")
                print("📝 生成HTML报告...")

                # 生成HTML可视化报告
                html_path = self._generate_ai_html_report(analysis_result)
                print(f"📄 HTML报告路径: {html_path}")

                self.ai_status_var.set("✅ AI分析完成！点击查看报告")
                print("✅ AI分析完成！")

                # 自动打开报告
                if html_path and os.path.exists(html_path):
                    os.startfile(str(html_path))
            else:
                print("❌ AI分析失败")
                self.ai_status_var.set("❌ AI分析失败")

        except Exception as e:
            print(f"❌ AI分析错误: {e}")
            import traceback
            traceback.print_exc()
            self.ai_status_var.set(f"❌ 分析出错: {str(e)}")

    def create_progress_section(self, parent):
        """创建进度条区域"""
        progress_frame = tk.Frame(parent, bg=self.colors['light'])
        progress_frame.pack(fill=tk.X, pady=(0, 20))

        # 进度条标题
        progress_title = tk.Label(
            progress_frame,
            text="检测进度",
            font=("Microsoft YaHei", 12, "bold"),
            fg=self.colors['dark'],
            bg=self.colors['light']
        )
        progress_title.pack(anchor=tk.W, pady=(0, 10))

        # 进度条
        style = ttk.Style()
        style.theme_use('clam')
        style.configure(
            "Detection.Horizontal.TProgressbar",
            background=self.colors['primary'],
            troughcolor=self.colors['light'],
            borderwidth=0,
            lightcolor=self.colors['primary'],
            darkcolor=self.colors['primary']
        )

        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=600,
            mode='determinate',
            style="Detection.Horizontal.TProgressbar"
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))

        # 进度文本
        self.progress_text = tk.Label(
            progress_frame,
            textvariable=self.status_var,
            font=("Microsoft YaHei", 10),
            fg=self.colors['gray'],
            bg=self.colors['light']
        )
        self.progress_text.pack(anchor=tk.W)

    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = tk.Frame(parent, bg=self.colors['dark'], height=30)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        status_frame.pack_propagate(False)

        # 状态文本
        status_label = tk.Label(
            status_frame,
            text="系统就绪 | 星星陪玩店选手作弊检测系统 v2.0",
            font=("Microsoft YaHei", 9),
            fg=self.colors['light'],
            bg=self.colors['dark']
        )
        status_label.pack(side=tk.LEFT, padx=15, pady=5)

        # 版权信息
        copyright_label = tk.Label(
            status_frame,
            text="© 2025 星星陪玩店技术部",
            font=("Microsoft YaHei", 9),
            fg=self.colors['gray'],
            bg=self.colors['dark']
        )
        copyright_label.pack(side=tk.RIGHT, padx=15, pady=5)

    def _lighten_color(self, color):
        """使颜色变浅"""
        color_map = {
            self.colors['primary']: '#ec7063',
            self.colors['secondary']: '#d5524a',
            self.colors['accent']: '#f8c471'
        }
        return color_map.get(color, color)

    def _darken_color(self, color):
        """使颜色变深"""
        color_map = {
            self.colors['primary']: '#c0392b',
            self.colors['secondary']: '#a93226',
            self.colors['accent']: '#d68910'
        }
        return color_map.get(color, color)

    def start_detection(self):
        """开始作弊检测"""
        if self.is_running:
            messagebox.showwarning("警告", "检测正在进行中，请稍候...")
            return

        # 在新线程中运行
        thread = threading.Thread(target=self._detection_thread)
        thread.daemon = True
        thread.start()

    def _detection_thread(self):
        """检测线程函数"""
        try:
            self.is_running = True
            self.detect_btn.configure(state=tk.DISABLED)

            # 更新状态
            self.status_var.set("正在初始化检测模块...")
            self.progress_var.set(0)

            # 创建工具集成器
            integrator = ToolsIntegrator(self.tools_path)

            # 收集数据
            def progress_callback(percent, message):
                # 将技术术语转换为检测术语
                detection_messages = {
                    "正在收集设备信息...": "正在扫描硬件指纹...",
                    "正在收集驱动程序信息...": "正在验证驱动完整性...",
                    "正在收集活动记录...": "正在分析行为模式...",
                    "数据收集完成": "检测数据收集完成"
                }
                display_message = detection_messages.get(message, message)

                self.progress_var.set(percent)
                self.status_var.set(display_message)
                self.root.update_idletasks()

            data = integrator.collect_all_data(progress_callback)

            # 生成时间戳
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 创建文件管理器并保存详细数据
            self.status_var.set("正在生成检测报告...")
            self.progress_var.set(85)
            self.root.update_idletasks()

            file_manager = CheatFileManager(self.output_path)
            file_manager.save_all_data(data)

            # 生成作弊检测报告
            self.status_var.set("正在生成分析报告...")
            self.progress_var.set(90)
            self.root.update_idletasks()

            report_generator = CheatReportGenerator()
            report_generator.generate_all_reports(data, self.output_path)

            # 完成
            self.progress_var.set(100)
            self.status_var.set(f"作弊检测完成")

            # 询问是否打开报告
            if messagebox.askyesno("检测完成", f"作弊检测已完成！\n\n生成的报告包括:\n- 硬件指纹分析报告\n- 驱动完整性验证报告\n- 行为模式分析报告\n- 综合检测汇总报告\n\n是否立即查看检测报告？"):
                import os
                os.startfile(str(self.output_path))

        except Exception as e:
            messagebox.showerror("错误", f"检测过程中发生错误:\n{str(e)}")
            self.status_var.set("检测失败")
        finally:
            self.is_running = False
            self.detect_btn.configure(state=tk.NORMAL)

    def open_reports_folder(self):
        """打开检测报告文件夹"""
        if sys.platform == "win32":
            os.startfile(str(self.output_path))
        else:
            webbrowser.open(f"file://{self.output_path}")

    def _find_latest_reports(self):
        """查找最新的检测报告文件"""
        try:
            # 使用已设置的输出路径
            report_dir = self.output_path if hasattr(self, 'output_path') and self.output_path else None

            # 如果没有设置输出路径，尝试查找
            if not report_dir:
                import os
                try:
                    desktop_path = Path(os.path.expanduser("~")) / "Desktop"
                    # 尝试查找中文和英文文件夹
                    possible_dirs = [
                        desktop_path / "星星陪玩店_反作弊检测报告",
                        desktop_path / "StarCafe_AntiCheat_Reports"
                    ]
                    for dir_path in possible_dirs:
                        if dir_path.exists():
                            report_dir = dir_path
                            break
                except:
                    pass

            print(f"🔍 搜索报告目录: {report_dir.absolute()}")

            if not report_dir.exists():
                print(f"❌ 报告目录不存在: {report_dir}")
                return None

            # 查找三个核心报告文件
            report_files = {}

            # 查找驱动报告
            driver_files = list(report_dir.glob("*驱动完整性*报告*.csv"))
            print(f"🔍 找到驱动报告文件: {len(driver_files)}个")
            if driver_files:
                report_files['driver'] = max(driver_files, key=lambda x: x.stat().st_mtime)
                print(f"📄 最新驱动报告: {report_files['driver']}")

            # 查找行为报告
            behavior_files = list(report_dir.glob("*行为模式*报告*.csv"))
            print(f"🔍 找到行为报告文件: {len(behavior_files)}个")
            if behavior_files:
                report_files['behavior'] = max(behavior_files, key=lambda x: x.stat().st_mtime)
                print(f"📄 最新行为报告: {report_files['behavior']}")

            # 查找硬件报告
            hardware_files = list(report_dir.glob("*硬件指纹*报告*.csv"))
            print(f"🔍 找到硬件报告文件: {len(hardware_files)}个")
            if hardware_files:
                report_files['hardware'] = max(hardware_files, key=lambda x: x.stat().st_mtime)
                print(f"📄 最新硬件报告: {report_files['hardware']}")

            print(f"📊 总共找到 {len(report_files)} 个报告文件")
            return report_files if len(report_files) >= 2 else None

        except Exception as e:
            print(f"❌ 查找报告文件错误: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _call_claude_ai(self, report_files):
        """调用AI进行分析"""
        try:
            from openai import OpenAI

            # 初始化OpenAI客户端
            client = OpenAI(
                base_url="https://yunwu.ai/v1",
                api_key=self.api_key_var.get().strip(),
                timeout=120
            )

            # AI分析提示词
            system_prompt = """# AI 游戏外挂检测分析助手提示词

## 角色定义

你是一个专业的游戏外挂检测AI系统。你的职责是分析用户提供的报告文件，判断玩家是否存在使用外挂或作弊行为的可能性，并提供详细的分析依据。

## 输入数据

你将分析三类报告文件：
1. **驱动报告** - 包含用户系统驱动程序信息、异常驱动加载记录等
2. **行为报告** - 记录用户的操作行为，如打开/关闭文件，启动程序等系统操作记录
3. **硬件报告** - 主要针对DMA设备进行摸排，标记可疑设备的ID和串口信息等

## 分析方法

分析时应关注以下方面：
- 驱动层面的可疑项目（未签名驱动、已知作弊工具相关驱动）
- 行为数据中的可疑操作（游戏运行期间访问或修改游戏文件，运行可疑程序）
- DMA设备排查（外接设备ID、串口记录、设备变动情况）
- 设备标记与历史比对（记录当前设备特征，与历史数据比对是否存在变化）

## 分析流程

1. 先分别总结三份报告中的关键信息
2. 识别驱动层面的可疑项目和异常加载行为
3. 分析用户操作记录中与游戏文件交互的可疑行为
4. 重点检查DMA设备信息，标记所有设备ID和串口特征
5. 结合游戏特性（APEX、无畏契约、三角洲行动、守望先锋）评估可疑程度
6. 给出综合结论和具体理由

## 输出格式

```
# 游戏外挂检测分析报告

## 文件分析摘要
- 驱动报告：[关键发现]
- 行为报告：[关键发现]
- 硬件报告：[关键发现，特别是DMA设备情况]

## 可疑点分析
[详细列出发现的可疑驱动、操作和设备]

## DMA设备记录
[列出所有检测到的DMA设备ID和串口信息，标记可疑项]

## 游戏特定分析
[针对用户玩的具体游戏进行专门分析]

## 结论
外挂可能性评级：[低/中/高]

## 理由说明
[详细解释得出结论的关键依据]

## 后续建议
[建议下次检测重点关注的设备和ID变化]
```

## 专业知识

你应具备以下外挂检测专业知识：
- 常见外挂驱动特征和隐藏方式
- DMA外挂工作原理及检测方法（设备ID变动、串口变化特征）
- 各类游戏常见外挂工具的系统层操作特征
- 针对APEX、无畏契约、三角洲行动、守望先锋等游戏的特定外挂识别要点

分析时应特别注意：
- DMA设备的完整记录是关键，任何ID或串口变动都可能是更换DMA外挂设备的证据
- 多次检测的对比分析更有说服力，应标记当前所有设备以便后续比对
- 综合驱动、操作行为和硬件信息三方面证据，给出客观合理的判断"""

            # 读取报告文件内容
            report_contents = {}
            for report_type, file_path in report_files.items():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()[:1500]  # 限制长度
                        report_contents[report_type] = content
                except Exception as e:
                    print(f"读取{report_type}报告失败: {e}")

            # 构建用户消息
            user_message = "请分析以下检测报告：\n\n"

            if 'driver' in report_contents:
                user_message += f"## 驱动报告\n{report_contents['driver']}\n\n"
            if 'behavior' in report_contents:
                user_message += f"## 行为报告\n{report_contents['behavior']}\n\n"
            if 'hardware' in report_contents:
                user_message += f"## 硬件报告\n{report_contents['hardware']}\n\n"

            # 调用AI分析
            response = client.chat.completions.create(
                model="claude-3-5-sonnet-20241022",
                messages=[
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": user_message
                    }
                ],
                max_tokens=3000,
                temperature=0.7
            )

            if response and response.choices:
                return response.choices[0].message.content
            else:
                print("AI API调用失败")
                return None

        except Exception as e:
            print(f"调用Claude AI错误: {e}")
            return None

    def _generate_ai_html_report(self, analysis_result):
        """生成AI分析的HTML可视化报告"""
        try:
            import datetime

            # 创建HTML内容
            timestamp = datetime.datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")

            html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能分析报告 - 星星陪玩店</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        .header p {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        .content {{
            padding: 40px;
        }}
        .analysis-content {{
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
            white-space: pre-wrap;
            font-size: 16px;
            line-height: 1.8;
        }}
        .footer {{
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 14px;
        }}
        .timestamp {{
            color: #6c757d;
            font-size: 14px;
            text-align: right;
            margin-bottom: 20px;
        }}
        .warning {{
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI智能分析报告</h1>
            <p>星星陪玩店 - 反作弊检测系统</p>
        </div>

        <div class="content">
            <div class="timestamp">生成时间: {timestamp}</div>

            <div class="warning">
                <strong>⚠️ 重要提醒：</strong> 本报告由AI系统自动生成，仅供参考。最终判断需结合人工审核。
            </div>

            <div class="analysis-content">
{analysis_result}
            </div>
        </div>

        <div class="footer">
            <p>© 2025 星星陪玩店反作弊检测系统 | Powered by Claude AI</p>
        </div>
    </div>

    <script>
        // 自动复制分析结果到剪贴板
        function copyToClipboard() {{
            const content = document.querySelector('.analysis-content').textContent;
            navigator.clipboard.writeText(content).then(() => {{
                console.log('分析结果已复制到剪贴板');
            }});
        }}

        // 页面加载完成后自动复制
        window.addEventListener('load', copyToClipboard);
    </script>
</body>
</html>"""

            # 保存HTML文件 - 使用统一的输出路径
            report_dir = self.output_path if hasattr(self, 'output_path') and self.output_path else None

            # 如果没有设置输出路径，使用备用方案
            if not report_dir:
                import os
                try:
                    desktop_path = Path(os.path.expanduser("~")) / "Desktop"
                    report_dir = desktop_path / "StarCafe_AntiCheat_Reports"
                except:
                    # 最后备用：程序目录
                    report_dir = Path('.') / "Reports"

            # 确保目录存在
            report_dir.mkdir(exist_ok=True)

            # HTML文件使用英文名称避免路径问题
            html_filename = f"AI_Analysis_Report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            html_path = report_dir / html_filename

            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(f"✅ AI分析报告已生成: {html_path}")
            print(f"📁 报告保存目录: {report_dir}")
            return html_path

        except Exception as e:
            print(f"生成HTML报告错误: {e}")
            return None

    def run(self):
        """运行应用程序"""
        try:
            print("🚀 开始运行主循环...")

            # 添加窗口关闭事件处理
            def on_closing():
                print("🔚 用户请求关闭窗口")
                try:
                    self.root.quit()
                    self.root.destroy()
                except:
                    pass

            self.root.protocol("WM_DELETE_WINDOW", on_closing)

            # 强制防止闪退的机制
            def keep_alive():
                try:
                    if self.root.winfo_exists():
                        # 每秒检查一次窗口状态
                        self.root.after(1000, keep_alive)
                except:
                    pass

            keep_alive()

            print("✓ 进入主循环...")
            self.root.mainloop()
            print("✓ 主循环正常退出")

        except Exception as e:
            print(f"❌ 主循环运行失败: {e}")
            import traceback
            traceback.print_exc()

            # 强制保持程序运行
            try:
                print("🔧 尝试强制保持程序运行...")
                import time
                while True:
                    try:
                        if not self.root.winfo_exists():
                            break
                        self.root.update()
                        time.sleep(0.1)
                    except:
                        break
            except:
                pass

def main():
    """主函数"""
    app = CheatDetectorGUI()
    app.run()

if __name__ == "__main__":
    main()
