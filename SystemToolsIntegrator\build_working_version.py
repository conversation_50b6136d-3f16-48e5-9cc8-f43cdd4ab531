#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建能正常工作的基础版本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def build_working_exe():
    """构建能正常工作的EXE"""
    
    print("🔨 构建能正常工作的基础版本...")
    
    # 创建PyInstaller spec
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os

block_cipher = None

a = Analysis(
    ['cheat_detector_gui.py'],
    pathex=[os.path.abspath('.')],
    binaries=[
        ('DevManView.exe', '.'),
        ('DriverView.exe', '.'),
        ('LastActivityView.exe', '.'),
        ('1.ico', '.'),
    ],
    datas=[
        ('1.ico', '.'),
        ('DevManView.exe', '.'),
        ('DriverView.exe', '.'),
        ('LastActivityView.exe', '.'),
    ],
    hiddenimports=[
        'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog', 'tkinter.font',
        'csv', 'json', 'threading', 'subprocess', 'tempfile', 'pathlib', 'datetime',
        'openpyxl', 'openpyxl.styles', 'openpyxl.styles.fonts', 'openpyxl.styles.fills', 'openpyxl.styles.alignment',
        'openai', 'requests', 'urllib3', 'certifi', 'charset_normalizer', 'idna',
        'chardet', 'chardet.universaldetector', 'chardet.charsetprober',
        'os', 'sys', 'hashlib', 'base64', 'time', 'zlib', 're', 'traceback', 'platform', 'locale', 'getpass',
        'encodings', 'encodings.utf_8', 'encodings.cp1252', 'encodings.gbk',
        'ctypes', 'ctypes.wintypes', 'ctypes.windll',
        'pkg_resources', 'importlib', 'collections', 'functools', 'itertools', 'weakref',
        'tools_integrator', 'cheat_report_generator', 'cheat_file_manager', 'uac_helper'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib', 'numpy', 'scipy', 'pandas', 'PIL', 
        'PyQt5', 'PyQt6', 'PySide2', 'PySide6',
        'test', 'tests', 'unittest', 'pytest',
        'icon_fix'  # 排除有问题的icon_fix模块
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
    name='星星陪玩店_反作弊检测系统_工作版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False, 
    upx=False,  # 先不压缩，确保稳定
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    uac_admin=False,
    icon='1.ico'
)
'''
    
    spec_path = 'working.spec'
    with open(spec_path, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    try:
        # 清理旧文件
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        print("🧹 清理完成，开始构建...")
        
        # 构建
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            spec_path
        ], check=True, capture_output=True, text=True)
        
        print("✓ 基础版本构建成功")
        
        # 检查结果
        exe_path = Path('dist/星星陪玩店_反作弊检测系统_工作版.exe')
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)
            print(f"✅ 工作版本构建完成!")
            print(f"📁 文件路径: {exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            
            # 清理临时文件
            os.remove(spec_path)
            if os.path.exists('build'):
                shutil.rmtree('build')
            
            return str(exe_path)
        else:
            print("❌ 未找到生成的EXE")
            return None
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print("错误输出:", e.stderr)
        return None

def main():
    """主函数"""
    print("=" * 70)
    print("构建能正常工作的基础版本")
    print("=" * 70)
    
    print("📋 构建特性:")
    print("- ✅ 基于原始Python源码")
    print("- ✅ 排除有问题的icon_fix模块")
    print("- ✅ 无压缩，确保稳定性")
    print("- ✅ 完整功能保留")
    print("- ✅ 无控制台，专业外观")
    
    # 构建工作版本
    working_exe = build_working_exe()
    
    if working_exe:
        print("\n🎊 工作版本构建完成!")
        print(f"📁 可用的EXE: {working_exe}")
        print("\n📋 下一步:")
        print("1. 先测试这个工作版本是否能正常运行")
        print("2. 确认功能正常后，再考虑添加代码保护")
        print("3. 保护时会基于这个稳定版本进行")
        
        print("\n✨ 工作版本特点:")
        print("- 基于稳定的Python源码构建")
        print("- 移除了可能导致崩溃的模块")
        print("- 保留所有核心功能")
        print("- 可以正常启动和使用")
    else:
        print("\n❌ 工作版本构建失败!")

if __name__ == "__main__":
    main()
