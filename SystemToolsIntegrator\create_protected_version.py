#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于工作版本创建简单保护版本
"""

import os
import sys
import shutil
import hashlib
import base64
import subprocess
import tempfile
from pathlib import Path

def create_simple_protection(exe_path):
    """创建简单保护版本"""
    
    print(f"🔒 为工作版本添加简单保护: {exe_path}")
    
    # 读取原始EXE
    with open(exe_path, 'rb') as f:
        original_data = f.read()
    
    print(f"✓ 读取EXE: {len(original_data)} 字节")
    
    # 计算哈希
    file_hash = hashlib.sha256(original_data).hexdigest()
    print(f"✓ 文件哈希: {file_hash[:16]}...")
    
    # 简单加密：Base64 + XOR
    xor_key = b'StarCafe2025Protection'
    xor_data = bytearray()
    for i, byte in enumerate(original_data):
        xor_data.append(byte ^ xor_key[i % len(xor_key)])
    
    # Base64编码
    encrypted_data = base64.b64encode(bytes(xor_data)).decode('utf-8')
    
    print(f"✓ 简单加密完成: {len(original_data)} → {len(encrypted_data)} 字符")
    
    # 创建保护壳
    protection_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单保护的EXE启动器
"""

import os
import sys
import base64
import tempfile
import subprocess
import hashlib

# 加密的EXE数据
ENCRYPTED_DATA = """{encrypted_data}"""

# 原始文件哈希
ORIGINAL_HASH = "{file_hash}"

# 解密密钥
XOR_KEY = b'StarCafe2025Protection'

def decrypt_and_run():
    """解密并运行"""
    try:
        # Base64解码
        xor_data = base64.b64decode(ENCRYPTED_DATA.encode('utf-8'))
        
        # XOR解密
        original_data = bytearray()
        for i, byte in enumerate(xor_data):
            original_data.append(byte ^ XOR_KEY[i % len(XOR_KEY)])
        
        # 验证哈希
        temp_hash = hashlib.sha256(bytes(original_data)).hexdigest()
        if temp_hash != ORIGINAL_HASH:
            return False
        
        # 创建临时文件并运行
        import uuid
        temp_name = f"tmp_{{uuid.uuid4().hex[:8]}}.exe"
        temp_path = os.path.join(tempfile.gettempdir(), temp_name)
        
        with open(temp_path, 'wb') as f:
            f.write(bytes(original_data))
        
        try:
            # 运行程序
            result = subprocess.run([temp_path], check=False)
            return True
        finally:
            # 清理临时文件
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
            except:
                pass
        
    except Exception:
        return False

if __name__ == "__main__":
    decrypt_and_run()
'''
    
    return protection_code

def build_protected_exe(protection_code):
    """构建受保护的EXE"""
    
    print("🔨 构建受保护的EXE...")
    
    # 创建临时Python文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as temp_py:
        temp_py.write(protection_code)
        temp_py_path = temp_py.name
    
    # 创建spec文件
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
import sys
import os

block_cipher = None

a = Analysis(
    [r'{temp_py_path}'],
    pathex=[os.path.abspath('.')],
    binaries=[],
    datas=[],
    hiddenimports=['tempfile', 'subprocess', 'hashlib', 'base64', 'uuid'],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
    name='星星陪玩店_反作弊检测系统_简单保护版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False, 
    upx=False,
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    uac_admin=False,
    icon='1.ico' if os.path.exists('1.ico') else None
)
'''
    
    spec_path = 'protected.spec'
    with open(spec_path, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    try:
        # 清理旧文件
        if os.path.exists('build'):
            shutil.rmtree('build')
        
        # 构建
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            spec_path
        ], check=True, capture_output=True, text=True)
        
        print("✓ 构建成功")
        
        # 检查结果
        protected_exe_path = Path('dist/星星陪玩店_反作弊检测系统_简单保护版.exe')
        if protected_exe_path.exists():
            file_size = protected_exe_path.stat().st_size / (1024 * 1024)
            print(f"✅ 简单保护版构建完成!")
            print(f"📁 文件路径: {protected_exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            
            # 清理临时文件
            os.remove(spec_path)
            os.remove(temp_py_path)
            if os.path.exists('build'):
                shutil.rmtree('build')
            
            return str(protected_exe_path)
        else:
            print("❌ 未找到生成的EXE")
            return None
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 70)
    print("基于工作版本创建简单保护版本")
    print("=" * 70)
    
    # 查找工作版本
    working_exe = "dist/星星陪玩店_反作弊检测系统_工作版.exe"
    if not os.path.exists(working_exe):
        print("❌ 未找到工作版本EXE")
        return
    
    print(f"🎯 基于工作版本: {working_exe}")
    
    print("\\n🔒 简单保护特性:")
    print("- ✅ Base64 + XOR双层加密")
    print("- ✅ SHA256完整性验证")
    print("- ✅ 临时文件安全运行")
    print("- ✅ 自动清理临时文件")
    print("- ✅ 基于稳定的工作版本")
    print("- ✅ 不会影响程序功能")
    
    # 创建保护代码
    protection_code = create_simple_protection(working_exe)
    
    # 构建受保护的EXE
    protected_exe = build_protected_exe(protection_code)
    
    if protected_exe:
        print("\\n🎊 简单保护版本完成!")
        print(f"📁 受保护的EXE: {protected_exe}")
        print("\\n✨ 保护效果:")
        print("- 原始EXE被加密存储")
        print("- 运行时动态解密")
        print("- 完整性验证保护")
        print("- 基于稳定版本，确保能正常运行")
        print("- 文件大小适中，启动速度快")
    else:
        print("\\n❌ 简单保护版本创建失败!")

if __name__ == "__main__":
    main()
